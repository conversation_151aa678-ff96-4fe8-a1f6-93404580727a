/**
 * WebSocket Server for Valorant Strategy Board
 * Handles real-time collaboration between clients
 */

const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

// Server configuration
const PORT = process.env.PORT || 8080;
const CORS_ORIGIN = process.env.CORS_ORIGIN || '*';

// Session and client management
const sessions = new Map(); // sessionCode -> Session object
const clients = new Map();  // ws -> Client object

class Session {
    constructor(sessionCode, hostClientId, sessionName = 'Strategy Session') {
        this.sessionCode = sessionCode;
        this.sessionName = sessionName;
        this.hostClientId = hostClientId;
        this.clients = new Map(); // clientId -> Client object
        this.createdAt = new Date();
        this.lastActivity = new Date();
    }

    addClient(client) {
        this.clients.set(client.id, client);
        this.lastActivity = new Date();
        console.log(`Client ${client.id} joined session ${this.sessionCode}. Total clients: ${this.clients.size}`);
    }

    removeClient(clientId) {
        const removed = this.clients.delete(clientId);
        this.lastActivity = new Date();
        if (removed) {
            console.log(`Client ${clientId} left session ${this.sessionCode}. Total clients: ${this.clients.size}`);
        }
        return removed;
    }

    broadcast(data, excludeClientId = null) {
        let sentCount = 0;
        this.clients.forEach((client) => {
            if (client.id !== excludeClientId && client.ws.readyState === WebSocket.OPEN) {
                try {
                    client.ws.send(JSON.stringify(data));
                    sentCount++;
                } catch (error) {
                    console.error(`Error sending to client ${client.id}:`, error);
                }
            }
        });
        return sentCount;
    }

    isEmpty() {
        return this.clients.size === 0;
    }

    isHost(clientId) {
        return this.hostClientId === clientId;
    }
}

class Client {
    constructor(ws, id = null) {
        this.ws = ws;
        this.id = id || uuidv4();
        this.userId = null;
        this.userName = null;
        this.sessionCode = null;
        this.connectedAt = new Date();
        this.lastActivity = new Date();
    }

    send(data) {
        if (this.ws.readyState === WebSocket.OPEN) {
            try {
                this.ws.send(JSON.stringify(data));
                return true;
            } catch (error) {
                console.error(`Error sending to client ${this.id}:`, error);
                return false;
            }
        }
        return false;
    }

    updateActivity() {
        this.lastActivity = new Date();
    }
}

// Utility functions
function generateSessionCode() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code = '';
    for (let i = 0; i < 6; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
}

function cleanupInactiveSessions() {
    const now = new Date();
    const maxInactiveTime = 30 * 60 * 1000; // 30 minutes

    sessions.forEach((session, sessionCode) => {
        if (session.isEmpty() || (now - session.lastActivity) > maxInactiveTime) {
            console.log(`Cleaning up inactive session: ${sessionCode}`);
            sessions.delete(sessionCode);
        }
    });
}

// Create WebSocket server
const wss = new WebSocket.Server({
    port: PORT,
    verifyClient: (info) => {
        // Basic CORS check
        const origin = info.origin;
        if (CORS_ORIGIN === '*') return true;
        return origin === CORS_ORIGIN;
    }
});

console.log(`WebSocket server started on port ${PORT}`);
console.log(`CORS origin: ${CORS_ORIGIN}`);

// Handle new connections
wss.on('connection', (ws, request) => {
    const client = new Client(ws);
    clients.set(ws, client);
    
    console.log(`New client connected: ${client.id}`);

    // Send connection confirmation
    client.send({
        type: 'connection-established',
        clientId: client.id,
        timestamp: new Date().toISOString()
    });

    // Handle incoming messages
    ws.on('message', (data) => {
        try {
            const message = JSON.parse(data);
            handleMessage(client, message);
        } catch (error) {
            console.error('Error parsing message:', error);
            client.send({
                type: 'error',
                message: 'Invalid message format',
                timestamp: new Date().toISOString()
            });
        }
    });

    // Handle client disconnect
    ws.on('close', () => {
        handleClientDisconnect(client);
    });

    // Handle connection errors
    ws.on('error', (error) => {
        console.error(`WebSocket error for client ${client.id}:`, error);
        handleClientDisconnect(client);
    });
});

// Handle incoming messages from clients
function handleMessage(client, message) {
    client.updateActivity();
    
    switch (message.type) {
        case 'create-session':
            handleCreateSession(client, message);
            break;
        case 'join-session':
            handleJoinSession(client, message);
            break;
        case 'leave-session':
            handleLeaveSession(client, message);
            break;
        case 'user-join':
            handleUserJoin(client, message);
            break;
        case 'user-leave':
            handleUserLeave(client, message);
            break;
        case 'canvas-change':
        case 'canvas-sync-request':
        case 'canvas-sync-data':
        case 'map-change':
        case 'cursor-move':
        case 'chat-message':
            handleCollaborationMessage(client, message);
            break;
        case 'ping':
            // Handle heartbeat ping - respond with pong
            client.send({
                type: 'pong',
                timestamp: new Date().toISOString()
            });
            break;
        default:
            console.log(`Unknown message type: ${message.type}`);
            client.send({
                type: 'error',
                message: `Unknown message type: ${message.type}`,
                timestamp: new Date().toISOString()
            });
    }
}

// Handle session creation
function handleCreateSession(client, message) {
    let sessionCode;
    let attempts = 0;
    const maxAttempts = 10;

    // Generate unique session code
    do {
        sessionCode = generateSessionCode();
        attempts++;
    } while (sessions.has(sessionCode) && attempts < maxAttempts);

    if (attempts >= maxAttempts) {
        client.send({
            type: 'session-creation-failed',
            message: 'Failed to generate unique session code',
            timestamp: new Date().toISOString()
        });
        return;
    }

    // Create new session
    const session = new Session(sessionCode, client.id, message.sessionName);
    sessions.set(sessionCode, session);
    
    // Add client to session
    client.sessionCode = sessionCode;
    session.addClient(client);

    console.log(`Session created: ${sessionCode} by client ${client.id}`);

    // Send success response
    client.send({
        type: 'session-created',
        sessionCode: sessionCode,
        sessionName: session.sessionName,
        isHost: true,
        timestamp: new Date().toISOString()
    });
}

// Handle session join
function handleJoinSession(client, message) {
    const sessionCode = message.sessionCode?.toUpperCase();
    
    if (!sessionCode || sessionCode.length !== 6) {
        client.send({
            type: 'session-join-failed',
            message: 'Invalid session code format',
            timestamp: new Date().toISOString()
        });
        return;
    }

    const session = sessions.get(sessionCode);
    if (!session) {
        client.send({
            type: 'session-join-failed',
            message: 'Session not found',
            timestamp: new Date().toISOString()
        });
        return;
    }

    // Add client to session
    client.sessionCode = sessionCode;
    session.addClient(client);

    console.log(`Client ${client.id} joined session ${sessionCode}`);

    // Send success response
    client.send({
        type: 'session-joined',
        sessionCode: sessionCode,
        sessionName: session.sessionName,
        isHost: session.isHost(client.id),
        timestamp: new Date().toISOString()
    });
}

// Handle session leave
function handleLeaveSession(client, message) {
    if (!client.sessionCode) {
        return;
    }

    const session = sessions.get(client.sessionCode);
    if (session) {
        // Notify other clients that user is leaving
        if (client.userId && client.userName) {
            session.broadcast({
                type: 'user-leave',
                userId: client.userId,
                userName: client.userName,
                timestamp: new Date().toISOString()
            }, client.id);
        }

        // Remove client from session
        session.removeClient(client.id);

        // Clean up empty session
        if (session.isEmpty()) {
            sessions.delete(client.sessionCode);
            console.log(`Session ${client.sessionCode} deleted (empty)`);
        }
    }

    client.sessionCode = null;
    client.userId = null;
    client.userName = null;

    // Send confirmation
    client.send({
        type: 'session-left',
        timestamp: new Date().toISOString()
    });
}

// Handle user join notification
function handleUserJoin(client, message) {
    if (!client.sessionCode) {
        client.send({
            type: 'error',
            message: 'Not in a session',
            timestamp: new Date().toISOString()
        });
        return;
    }

    // Update client info
    client.userId = message.userId;
    client.userName = message.userName;

    const session = sessions.get(client.sessionCode);
    if (session) {
        // Broadcast user join to other clients
        session.broadcast({
            type: 'user-join',
            userId: client.userId,
            userName: client.userName,
            timestamp: new Date().toISOString()
        }, client.id);

        console.log(`User ${client.userName} (${client.userId}) joined session ${client.sessionCode}`);
    }
}

// Handle user leave notification
function handleUserLeave(client, message) {
    if (!client.sessionCode) {
        return;
    }

    const session = sessions.get(client.sessionCode);
    if (session && client.userId && client.userName) {
        // Broadcast user leave to other clients
        session.broadcast({
            type: 'user-leave',
            userId: client.userId,
            userName: client.userName,
            timestamp: new Date().toISOString()
        }, client.id);

        console.log(`User ${client.userName} (${client.userId}) left session ${client.sessionCode}`);
    }
}

// Handle collaboration messages (canvas changes, cursor moves, etc.)
function handleCollaborationMessage(client, message) {
    if (!client.sessionCode) {
        client.send({
            type: 'error',
            message: 'Not in a session',
            timestamp: new Date().toISOString()
        });
        return;
    }

    const session = sessions.get(client.sessionCode);
    if (session) {
        // Add sender information to the message
        message.fromUserId = client.userId;
        message.fromUserName = client.userName;
        message.fromClientId = client.id;
        message.timestamp = new Date().toISOString();

        // Broadcast to all other clients in the session
        const sentCount = session.broadcast(message, client.id);

        // For debugging
        if (message.type !== 'cursor-move') {
            console.log(`Broadcasted ${message.type} from ${client.userName || client.id} to ${sentCount} clients`);
        }
    }
}

// Handle client disconnect
function handleClientDisconnect(client) {
    console.log(`Client disconnected: ${client.id}`);

    // Leave session if in one
    if (client.sessionCode) {
        handleLeaveSession(client, { type: 'leave-session' });
    }

    // Remove from clients map
    clients.delete(client.ws);
}

// Periodic cleanup of inactive sessions
setInterval(cleanupInactiveSessions, 5 * 60 * 1000); // Every 5 minutes

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\nShutting down WebSocket server...');

    // Close all client connections
    wss.clients.forEach((ws) => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.close(1000, 'Server shutting down');
        }
    });

    // Close server
    wss.close(() => {
        console.log('WebSocket server closed');
        process.exit(0);
    });
});

// Health check endpoint (if needed for load balancers)
process.on('SIGUSR1', () => {
    console.log(`Server status: ${wss.clients.size} connected clients, ${sessions.size} active sessions`);
});
