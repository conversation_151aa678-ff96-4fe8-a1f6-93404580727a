{"name": "valorant-strategy-board", "version": "2.0.0", "description": "Real-time collaborative strategy board for Valorant with Supabase integration", "main": "index.html", "scripts": {"dev": "vercel dev", "build": "node build-production.js", "start": "vercel dev", "deploy": "npm run build && vercel --prod", "local": "python -m http.server 8000", "setup-supabase": "echo 'Run the SQL script in supabase-setup.sql in your Supabase SQL editor'"}, "keywords": ["valorant", "strategy", "collaboration", "real-time", "supabase", "gaming"], "author": "Valorant Strategy Board Team", "license": "MIT", "dependencies": {}, "devDependencies": {"vercel": "^32.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/valorant-strategy-board.git"}, "homepage": "https://your-app.vercel.app", "vercel": {"functions": {"api/config.js": {"maxDuration": 10}}}}