# Deployment Guide for Valorant Strategy Board

## 🚀 Deploying to Vercel

### Prerequisites
- GitHub account
- Vercel account (free tier available)
- Your code pushed to a GitHub repository

### Step-by-Step Deployment

1. **Push your code to GitHub:**
   ```bash
   git init
   git add .
   git commit -m "Initial commit"
   git branch -M main
   git remote add origin https://github.com/yourusername/valo-strat-board.git
   git push -u origin main
   ```

2. **Deploy to Vercel:**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with your GitHub account
   - Click "New Project"
   - Import your GitHub repository
   - Vercel will automatically detect the configuration from `vercel.json`
   - Click "Deploy"

3. **Environment Variables (if needed):**
   - In Vercel dashboard, go to your project settings
   - Add environment variables:
     - `NODE_ENV`: `production`
     - `PORT`: `8080` (optional)

### Important Notes

- **WebSocket Support**: Vercel has limitations with WebSocket connections. For full WebSocket support, consider alternative hosting:
  - **Railway**: Excellent for Node.js apps with WebSocket support
  - **Render**: Good free tier with WebSocket support
  - **Heroku**: Reliable but paid plans required
  - **DigitalOcean App Platform**: Good performance and WebSocket support

### Alternative: Railway Deployment (Recommended for WebSockets)

1. **Install Railway CLI:**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login and deploy:**
   ```bash
   railway login
   railway init
   railway up
   ```

3. **Configure environment:**
   - Set `NODE_ENV=production`
   - Railway will automatically handle the port

### Alternative: Render Deployment

1. **Create `render.yaml`:**
   ```yaml
   services:
     - type: web
       name: valo-strat-board
       env: node
       buildCommand: cd server && npm install
       startCommand: cd server && npm start
       envVars:
         - key: NODE_ENV
           value: production
   ```

2. **Connect your GitHub repo to Render**
3. **Deploy automatically on push**

## 🔧 Local Development

### Running Locally
1. **Start the WebSocket server:**
   ```bash
   cd server
   npm install
   npm start
   ```

2. **Open the application:**
   - Open `index.html` in your browser
   - Or serve it with a local server:
   ```bash
   npx http-server . -p 3000
   ```

### Testing Collaboration
1. Open multiple browser tabs
2. Host a session in one tab
3. Join the session in other tabs
4. Test drawing, chat, and agent placement

## 📝 Configuration Notes

- **Local Development**: WebSocket connects to `localhost:8080`
- **Vercel**: Uses `/api` route for WebSocket (limited support)
- **Other Platforms**: Uses the same domain as the web app

## 🐛 Troubleshooting

### Common Issues:
1. **WebSocket connection fails**: Check if the server is running on port 8080
2. **CORS errors**: Ensure the server allows connections from your domain
3. **Vercel WebSocket issues**: Consider using Railway or Render instead

### Debug Steps:
1. Check browser console for errors
2. Verify server logs
3. Test WebSocket connection manually
4. Ensure all dependencies are installed
