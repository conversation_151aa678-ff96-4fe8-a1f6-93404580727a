# Valorant Strategy Board - Real-time Collaboration Tool

A professional, real-time collaborative strategy board for Valorant teams. Plan tactics, draw strategies, and coordinate with your team on all Valorant maps with live synchronization.

## 🚀 New Features

### ✨ Real-time Collaboration
- **WebSocket-powered**: Server-based connections for reliable collaboration
- **Cross-platform compatibility**: Works reliably across all browsers and devices
- **Firewall-friendly**: No issues with corporate networks or restrictive firewalls
- **Live synchronization**: See changes from teammates in real-time
- **User presence**: Track who's connected and active
- **Session management**: Easy-to-use host/join system with 6-character codes
- **Automatic reconnection**: Built-in retry logic for robust connectivity

### 🎨 Enhanced Editor
- **Fixed z-index issues**: Drawing tools and crosshair now work perfectly
- **Improved precision**: Better tool responsiveness and visual feedback
- **Enhanced eraser**: Smart eraser that preserves agent icons
- **Better layer management**: Proper stacking and interaction handling

### 🔍 SEO Optimized
- **Comprehensive meta tags**: Open Graph, Twitter Cards, and structured data
- **Search engine friendly**: Proper heading hierarchy and semantic HTML
- **Performance optimized**: Fast loading and responsive design
- **Accessibility features**: Screen reader friendly with proper ARIA labels

## 🎮 Core Features

### Interactive Map System
- Choose from all Valorant maps (Ascent, Bind, Haven, Split, Icebox, Pearl, Fracture, Lotus, Sunset, and Abyss)
- High-quality map images serve as the base layer for strategy planning
- Zoom and pan functionality for detailed planning

### Agent Placement
- Drag and drop all current Valorant agent icons onto the map
- Rotate and resize agents using intuitive controls
- Smart positioning with visual feedback

### Advanced Drawing Tools
- Pencil tool for freehand drawing with smooth curves
- Line and arrow tools for movement paths and callouts
- Rectangle and circle tools for marking areas and zones
- Multiple colors for different types of annotations
- Smart eraser that preserves agent icons
- Adjustable brush sizes for precise control

### Strategy Management
- Save strategies with custom names to local storage
- Load existing strategies instantly
- Delete unwanted strategies with confirmation
- Comprehensive undo/redo functionality (20 levels)
- Clear canvas option with confirmation

## 🚀 Getting Started

### Basic Usage
1. **Select a Map**: Choose your desired map from the dropdown at the top
2. **Place Agents**: Click on agent icons in the sidebar to add them to the map
3. **Draw Strategy Elements**:
   - Use the pencil tool for freehand drawing
   - Use line and arrow tools for movement paths
   - Use shape tools (rectangle, circle) to highlight areas
   - Change colors using the color picker
   - Adjust brush size with the slider
4. **Save Your Strategy**: Enter a name and click Save
5. **Load Strategies**: Click Load to access saved strategies

### 🤝 Real-time Collaboration
1. **Click the "👥 Collaborate" button** in the header
2. **Choose your mode**:
   - **Host Session**: Create a new collaboration session
     - Enter a session name (optional)
     - Click "Start Session"
     - Share the 6-character code with your team
   - **Join Session**: Connect to an existing session
     - Enter the 6-character code from your teammate
     - Click "Connect"
3. **Start collaborating**: All changes are synchronized in real-time!

### ⌨️ Keyboard Shortcuts
| Key | Action |
|-----|--------|
| `V` | Select tool |
| `P` | Pencil tool |
| `L` | Line tool |
| `A` | Arrow tool |
| `R` | Rectangle tool |
| `C` | Circle tool |
| `E` | Eraser tool |
| `Del/Backspace` | Delete selected object |
| `Ctrl+Z` | Undo |
| `Ctrl+Y` | Redo |
| `Ctrl++` | Zoom in |
| `Ctrl+-` | Zoom out |
| `Ctrl+0` | Reset zoom |

## 🛠️ Technical Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Node.js with WebSocket server for real-time collaboration
- **Canvas**: Fabric.js for high-performance drawing and manipulation
- **WebSockets**: Native WebSocket API for reliable real-time communication
- **Storage**: Browser localStorage for strategy persistence
- **Styling**: Custom CSS with Valorant-inspired design theme
- **SEO**: Comprehensive meta tags, structured data, and sitemap

## 📁 Project Structure

```
Strategy-board/
├── index.html              # Main application
├── server/
│   ├── package.json       # Server dependencies
│   └── server.js          # WebSocket server
├── css/
│   └── styles.css          # Application styles with collaboration UI
├── js/
│   ├── app.js             # Main application logic
│   ├── websocket-manager.js # WebSocket connection management
│   └── collaboration.js   # Real-time collaboration features
├── mini-map/              # Valorant map images
├── agent-icons/           # Valorant agent icons
├── sitemap.xml           # SEO sitemap
├── robots.txt            # Search engine directives
└── README.md             # Documentation
```

## 🔄 Migration from WebRTC to WebSocket

This version has been upgraded from a WebRTC-based peer-to-peer system to a more reliable WebSocket-based server architecture:

### Benefits of the New System:
- **Better reliability**: No more connection issues due to NAT/firewall restrictions
- **Corporate network friendly**: Works in restrictive network environments
- **Consistent performance**: Server-based routing eliminates peer-to-peer connectivity issues
- **Easier deployment**: Single server handles all client connections
- **Better scalability**: Can handle more concurrent users

### What Changed:
- Removed dependency on PeerJS library
- Added Node.js WebSocket server
- Maintained all existing collaboration features
- Improved connection stability and error handling

## 🔧 Development

### Local Development
1. Clone this repository
2. **Start the WebSocket server:**
   - **Windows:** Double-click `start-server.bat` or run `cd server && npm install && npm start`
   - **Mac/Linux:** Run `./start-server.sh` or `cd server && npm install && npm start`
3. Open `index.html` in your web browser
4. For collaboration testing, open multiple browser tabs/windows

### Browser Compatibility
- **Chrome 60+** (recommended for best WebSocket support)
- **Firefox 55+**
- **Safari 11+**
- **Edge 79+**

### Server Requirements
- **Node.js 14+** for running the WebSocket server
- **Modern browser** with WebSocket support
- **Internet connection** for real-time collaboration

## 🌐 Deployment

### SEO Setup
1. Update domain references in `index.html` meta tags
2. Replace `yourdomain.com` with your actual domain
3. Update `sitemap.xml` with your domain
4. Add favicon files to the root directory

### Production Considerations
- Deploy server with proper process management (PM2, Docker, etc.)
- Use environment variables for server configuration
- Set up load balancing for multiple server instances
- Configure proper CORS and security headers
- Optimize images for faster loading
- Set up proper caching headers

## 🎯 Supported Valorant Maps

- ✅ Ascent
- ✅ Bind
- ✅ Haven
- ✅ Split
- ✅ Icebox
- ✅ Pearl
- ✅ Fracture
- ✅ Lotus
- ✅ Sunset
- ✅ Abyss

## 🔮 Future Enhancements

- Voice chat integration
- Strategy templates and presets
- Team management system
- Cloud storage and sharing
- Mobile app version
- Advanced drawing tools
- Strategy replay system
- Tournament mode

---

**Built for the Valorant community** 🎮
*Plan better, play better, win together!*