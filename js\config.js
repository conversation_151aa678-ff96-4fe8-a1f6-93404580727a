/**
 * Configuration for Valorant Strategy Board
 * Handles environment variables and settings
 */

class Config {
    constructor() {
        this.isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
        this.supabaseUrl = null;
        this.supabaseKey = null;
        this.initialized = false;
    }
    
    /**
     * Initialize configuration
     */
    async initialize() {
        try {
            if (this.isProduction) {
                // In production, try to load from environment or API
                await this.loadProductionConfig();
            } else {
                // In development, use local config
                this.loadDevelopmentConfig();
            }
            
            this.initialized = true;
            console.log('Configuration initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize configuration:', error);
            return false;
        }
    }
    
    /**
     * Load production configuration
     */
    async loadProductionConfig() {
        try {
            // Try to load from Vercel environment variables via API route
            const response = await fetch('/api/config');
            if (response.ok) {
                const config = await response.json();
                this.supabaseUrl = config.supabaseUrl;
                this.supabaseKey = config.supabaseKey;
                return;
            }
        } catch (error) {
            console.warn('Failed to load config from API:', error);
        }
        
        // Fallback: try to load from global variables (set by build process)
        if (window.SUPABASE_URL && window.SUPABASE_ANON_KEY) {
            this.supabaseUrl = window.SUPABASE_URL;
            this.supabaseKey = window.SUPABASE_ANON_KEY;
            return;
        }
        
        // Last resort: show configuration modal
        this.showConfigurationModal();
    }
    
    /**
     * Load development configuration
     */
    loadDevelopmentConfig() {
        // For development, you can set these directly or load from localStorage
        const savedConfig = this.loadFromLocalStorage();
        
        if (savedConfig.supabaseUrl && savedConfig.supabaseKey) {
            this.supabaseUrl = savedConfig.supabaseUrl;
            this.supabaseKey = savedConfig.supabaseKey;
        } else {
            // Show configuration modal for first-time setup
            this.showConfigurationModal();
        }
    }
    
    /**
     * Load configuration from localStorage
     */
    loadFromLocalStorage() {
        try {
            const config = localStorage.getItem('valorant-strategy-config');
            return config ? JSON.parse(config) : {};
        } catch (error) {
            console.warn('Failed to load config from localStorage:', error);
            return {};
        }
    }
    
    /**
     * Save configuration to localStorage
     */
    saveToLocalStorage() {
        try {
            const config = {
                supabaseUrl: this.supabaseUrl,
                supabaseKey: this.supabaseKey
            };
            localStorage.setItem('valorant-strategy-config', JSON.stringify(config));
        } catch (error) {
            console.warn('Failed to save config to localStorage:', error);
        }
    }
    
    /**
     * Show configuration modal for manual setup
     */
    showConfigurationModal() {
        const modal = this.createConfigurationModal();
        document.body.appendChild(modal);
        modal.style.display = 'block';
    }
    
    /**
     * Create configuration modal
     */
    createConfigurationModal() {
        const modal = document.createElement('div');
        modal.className = 'modal config-modal';
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        modal.innerHTML = `
            <div class="modal-content" style="
                background: #1a1a1a;
                padding: 30px;
                border-radius: 10px;
                max-width: 500px;
                width: 90%;
                color: white;
                border: 1px solid #444;
            ">
                <h2 style="color: #ff4655; margin-bottom: 20px;">🔧 Supabase Configuration Required</h2>
                <p style="margin-bottom: 20px; line-height: 1.5;">
                    To enable real-time collaboration, please provide your Supabase project credentials.
                    You can find these in your Supabase project dashboard.
                </p>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Supabase URL:</label>
                    <input type="url" id="config-supabase-url" placeholder="https://your-project.supabase.co" 
                           style="width: 100%; padding: 10px; border: 1px solid #444; background: #2a2a2a; color: white; border-radius: 5px;">
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Supabase Anon Key:</label>
                    <input type="password" id="config-supabase-key" placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." 
                           style="width: 100%; padding: 10px; border: 1px solid #444; background: #2a2a2a; color: white; border-radius: 5px;">
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label style="display: flex; align-items: center; gap: 10px;">
                        <input type="checkbox" id="config-save-locally" checked>
                        <span>Save configuration locally (recommended for development)</span>
                    </label>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button id="config-skip" style="
                        padding: 10px 20px;
                        background: #666;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                    ">Skip (Offline Mode)</button>
                    <button id="config-save" style="
                        padding: 10px 20px;
                        background: #ff4655;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        cursor: pointer;
                    ">Save & Continue</button>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background: #2a2a2a; border-radius: 5px; font-size: 0.9em;">
                    <strong>📚 Setup Instructions:</strong><br>
                    1. Go to <a href="https://supabase.com" target="_blank" style="color: #ff4655;">supabase.com</a><br>
                    2. Create a new project or select existing one<br>
                    3. Go to Settings → API<br>
                    4. Copy the URL and anon/public key<br>
                    5. Run the SQL setup script in your Supabase SQL editor
                </div>
            </div>
        `;
        
        // Add event listeners
        const saveBtn = modal.querySelector('#config-save');
        const skipBtn = modal.querySelector('#config-skip');
        const urlInput = modal.querySelector('#config-supabase-url');
        const keyInput = modal.querySelector('#config-supabase-key');
        const saveLocallyCheckbox = modal.querySelector('#config-save-locally');
        
        saveBtn.addEventListener('click', () => {
            const url = urlInput.value.trim();
            const key = keyInput.value.trim();
            
            if (!url || !key) {
                alert('Please provide both Supabase URL and key');
                return;
            }
            
            this.supabaseUrl = url;
            this.supabaseKey = key;
            
            if (saveLocallyCheckbox.checked) {
                this.saveToLocalStorage();
            }
            
            modal.remove();
            this.initialized = true;
            
            // Trigger initialization of collaboration
            if (window.initializeSupabaseCollaboration) {
                window.initializeSupabaseCollaboration();
            }
        });
        
        skipBtn.addEventListener('click', () => {
            modal.remove();
            console.log('Skipped Supabase configuration - running in offline mode');
        });
        
        return modal;
    }
    
    /**
     * Check if configuration is valid
     */
    isValid() {
        return this.initialized && this.supabaseUrl && this.supabaseKey;
    }
    
    /**
     * Get Supabase configuration
     */
    getSupabaseConfig() {
        return {
            url: this.supabaseUrl,
            key: this.supabaseKey
        };
    }
}

// Create global config instance
window.appConfig = new Config();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.appConfig.initialize();
    });
} else {
    window.appConfig.initialize();
}
