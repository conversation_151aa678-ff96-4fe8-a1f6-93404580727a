# 🚀 Complete Deployment Guide - Valorant Strategy Board

This guide will walk you through deploying your Valorant Strategy Board to Vercel with Supabase integration.

## 📋 Prerequisites

- ✅ Supabase project created
- ✅ Vercel account
- ✅ Git repository (GitHub, GitLab, or Bitbucket)
- ✅ Node.js installed locally

## 🗄️ Step 1: Set Up Supabase Database

### 1.1 Run the Database Setup Script

1. **Open your Supabase project dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Navigate to your project: `cnpireeeqotnmbrctglr`

2. **Execute the SQL setup script**
   - Go to **SQL Editor** in the left sidebar
   - Copy the entire contents of `supabase-setup.sql`
   - Paste it into the SQL editor
   - Click **Run** to execute the script

3. **Enable Real-time**
   - Go to **Database** → **Replication**
   - Enable replication for these tables:
     - `sessions`
     - `canvas_objects`
     - `session_users`
     - `user_cursors`
     - `chat_messages`

### 1.2 Verify Database Setup

Check that these tables were created:
- ✅ `sessions`
- ✅ `canvas_objects`
- ✅ `session_users`
- ✅ `user_cursors`
- ✅ `chat_messages`

## 🔐 Step 2: Secure Your Credentials

### 2.1 For Local Development

**Option A: Use the local config file (Recommended)**
```bash
# The js/local-config.js file is already set up with your credentials
# Just make sure it's not committed to version control
```

**Option B: Use the configuration modal**
```bash
# Comment out the local config in js/local-config.js
# The app will show a modal to enter credentials
```

### 2.2 For Production

**Never commit credentials to your repository!**

Your credentials are:
- **Supabase URL**: `https://cnpireeeqotnmbrctglr.supabase.co`
- **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.q-LoWCfeXOuAAwhEhJGH_4RoiaCXMPS0S9TONU75Bew`

## 🌐 Step 3: Deploy to Vercel

### 3.1 Install Vercel CLI

```bash
npm install -g vercel
```

### 3.2 Login to Vercel

```bash
vercel login
```

### 3.3 Prepare for Deployment

1. **Build production files**
   ```bash
   npm run build
   ```

2. **Initialize Vercel project**
   ```bash
   vercel
   ```
   - Choose your scope
   - Link to existing project or create new one
   - Set root directory to `./` (current directory)
   - Override settings? **No**

### 3.4 Set Environment Variables

**Method 1: Using Vercel CLI**
```bash
vercel env add SUPABASE_URL
# Enter: https://cnpireeeqotnmbrctglr.supabase.co

vercel env add SUPABASE_ANON_KEY
# Enter: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.q-LoWCfeXOuAAwhEhJGH_4RoiaCXMPS0S9TONU75Bew
```

**Method 2: Using Vercel Dashboard**
1. Go to your project in [vercel.com](https://vercel.com)
2. Go to **Settings** → **Environment Variables**
3. Add these variables:
   - `SUPABASE_URL` = `https://cnpireeeqotnmbrctglr.supabase.co`
   - `SUPABASE_ANON_KEY` = `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.q-LoWCfeXOuAAwhEhJGH_4RoiaCXMPS0S9TONU75Bew`

### 3.5 Deploy to Production

```bash
vercel --prod
```

## 🔄 Step 4: Set Up Continuous Deployment (Optional)

### 4.1 Connect GitHub Repository

1. **Push your code to GitHub**
   ```bash
   git add .
   git commit -m "Add Supabase integration and production setup"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Click **New Project**
   - Import your GitHub repository
   - Set environment variables (same as above)
   - Deploy

### 4.2 Automatic Deployments

Now every push to your main branch will automatically deploy to Vercel!

## 🧪 Step 5: Test Your Deployment

### 5.1 Basic Functionality Test

1. **Open your deployed app**
   - Visit your Vercel URL (e.g., `https://your-app.vercel.app`)
   - The app should load without showing the configuration modal

2. **Test collaboration**
   - Click "👥 Collaborate" → "Host Session"
   - Note the session code
   - Open another browser/device
   - Join the session with the code

### 5.2 Feature Testing Checklist

- ✅ Maps load correctly
- ✅ Agent icons can be added and moved
- ✅ Drawing tools work
- ✅ Custom image upload works
- ✅ Real-time collaboration works
- ✅ Chat system works
- ✅ Session management works

## 🔧 Local Development Setup

### For Team Members

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd valorant-strategy-board
   ```

2. **Start local development**
   ```bash
   # Option 1: Simple HTTP server
   python -m http.server 8000
   
   # Option 2: Using npm
   npm run local
   
   # Option 3: Using Vercel (for testing API routes)
   npm run dev
   ```

3. **Configure Supabase**
   - The app will use the local config automatically
   - Or enter credentials in the configuration modal

## 🛠️ Troubleshooting

### Common Issues

1. **Configuration Modal Keeps Appearing**
   ```bash
   # Check that environment variables are set correctly
   vercel env ls
   
   # Verify API route works
   curl https://your-app.vercel.app/api/config
   ```

2. **Real-time Not Working**
   - Verify Supabase real-time is enabled for all tables
   - Check browser console for WebSocket errors
   - Ensure RLS policies are set correctly

3. **Images Not Loading**
   - Check CORS settings in Supabase
   - Verify image URLs are accessible
   - Try uploading images instead of external URLs

### Debug Mode

Add `?debug=true` to your URL to enable debug logging:
```
https://your-app.vercel.app?debug=true
```

## 📊 Monitoring and Analytics

### Vercel Analytics

1. **Enable Analytics**
   - Go to your Vercel project dashboard
   - Navigate to **Analytics** tab
   - Enable Web Analytics

2. **Monitor Performance**
   - Track page load times
   - Monitor API response times
   - Check error rates

### Supabase Monitoring

1. **Database Performance**
   - Go to Supabase dashboard
   - Check **Database** → **Logs**
   - Monitor query performance

2. **Real-time Usage**
   - Check **Database** → **Replication**
   - Monitor connection counts
   - Track message throughput

## 🔄 Updates and Maintenance

### Updating the App

1. **Make changes locally**
2. **Test thoroughly**
3. **Push to GitHub** (if using continuous deployment)
4. **Or deploy manually**:
   ```bash
   vercel --prod
   ```

### Database Migrations

If you need to update the database schema:
1. Create migration SQL scripts
2. Test in development first
3. Apply to production Supabase project
4. Update application code if needed

## 🎯 Production Checklist

Before going live:

- ✅ Database setup complete
- ✅ Environment variables configured
- ✅ Real-time enabled in Supabase
- ✅ All features tested
- ✅ Performance optimized
- ✅ Error handling tested
- ✅ Mobile responsiveness verified
- ✅ Cross-browser compatibility checked
- ✅ Security review completed

## 🚀 Your App is Ready!

Your Valorant Strategy Board is now production-ready with:

- **Reliable real-time collaboration** via Supabase
- **Scalable deployment** on Vercel
- **Secure credential management**
- **Automatic deployments** (if using GitHub integration)
- **Professional monitoring** and analytics

**Your deployment URL**: Will be provided after running `vercel --prod`

---

**Need help?** Check the troubleshooting section or open an issue in your repository.
