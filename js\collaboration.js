/**
 * Collaboration Manager for Valorant Strategy Board
 * Handles real-time synchronization of canvas changes and user interactions
 */

class CollaborationManager {
    constructor(canvas, websocketManager) {
        this.canvas = canvas;
        this.websocket = websocketManager;
        this.connectedUsers = new Map();
        this.userCursors = new Map();
        this.isCollaborating = false;
        this.lastSyncTime = 0;
        this.syncThrottle = 50; // Reduced throttle for better responsiveness
        this.pendingBroadcasts = new Map(); // Track pending broadcasts by object ID

        // Bind WebSocket event handlers
        this.setupWebSocketHandlers();

        // Bind canvas event handlers
        this.setupCanvasHandlers();

        console.log('Collaboration Manager initialized');
    }

    /**
     * Set up WebSocket event handlers
     */
    setupWebSocketHandlers() {
        this.websocket.onConnectionEstablished = () => {
            console.log('Collaboration connection established');
            this.isCollaborating = true;
            this.updateConnectionStatus('connected');
            this.showUsersPanel();
            this.showChatPanel();
            this.setupChatHandlers();
            console.log('Set collaboration status to connected');
        };

        this.websocket.onConnectionLost = () => {
            console.log('Collaboration connection lost - setting status to offline');
            this.connectedUsers.clear();
            this.userCursors.clear();
            if (this.isCollaborating) {
                this.isCollaborating = false;
                this.updateConnectionStatus('offline');
                this.hideUsersPanel();
                this.hideChatPanel();
                console.log('Set collaboration status to offline');
            }
        };

        this.websocket.onDataReceived = (data, fromClientId) => {
            this.handleIncomingData(data, fromClientId);
        };

        this.websocket.onUserJoined = (data) => {
            this.handleUserJoin(data, data.fromClientId || data.fromUserId);
        };

        this.websocket.onUserLeft = (data) => {
            this.handleUserLeave(data);
        };

        this.websocket.onError = (error) => {
            console.error('WebSocket error:', error);
            this.updateConnectionStatus('error');
        };
    }

    /**
     * Set up canvas event handlers for collaboration
     */
    setupCanvasHandlers() {
        // Track object modifications
        this.canvas.on('object:added', (e) => {
            if (this.isCollaborating && !this.canvas.isLoadingFromJSON && !e.target.isFromRemote) {
                // Ensure object has an ID
                if (!e.target.id) {
                    e.target.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                console.log('Broadcasting object:added', e.target.id, e.target);
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:added', e.target);
                }, e.target.id);
            }
        });

        this.canvas.on('object:modified', (e) => {
            console.log('Canvas object:modified event', e.target.id, 'isCollaborating:', this.isCollaborating, 'isFromRemote:', e.target.isFromRemote);
            if (this.isCollaborating && !e.target.isFromRemote) {
                // Ensure object has an ID
                if (!e.target.id) {
                    e.target.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                console.log('Broadcasting object:modified', e.target.id, e.target);
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:modified', e.target);
                }, e.target.id);
            }
        });

        // Also listen for scaling events specifically
        this.canvas.on('object:scaling', (e) => {
            console.log('Canvas object:scaling event', e.target.id, 'isCollaborating:', this.isCollaborating, 'isFromRemote:', e.target.isFromRemote);
            if (this.isCollaborating && !e.target.isFromRemote) {
                // Ensure object has an ID
                if (!e.target.id) {
                    e.target.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                console.log('Broadcasting object:scaling', e.target.id, e.target);
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:modified', e.target);
                }, e.target.id);
            }
        });

        this.canvas.on('object:moving', (e) => {
            console.log('Canvas object:moving event', e.target.id, 'isCollaborating:', this.isCollaborating, 'isFromRemote:', e.target.isFromRemote);
            if (this.isCollaborating && !e.target.isFromRemote) {
                // Ensure object has an ID
                if (!e.target.id) {
                    e.target.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                console.log('Broadcasting object:moving', e.target.id, e.target);
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:modified', e.target);
                }, e.target.id);
            }
        });

        this.canvas.on('object:removed', (e) => {
            console.log('Canvas object:removed event', e.target.id, 'isCollaborating:', this.isCollaborating, 'isFromRemote:', e.target.isFromRemote);
            if (this.isCollaborating && !e.target.isFromRemote) {
                // Ensure object has an ID
                if (!e.target.id) {
                    e.target.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                console.log('Broadcasting object:removed', e.target.id, e.target);
                this.throttledSync(() => {
                    this.broadcastCanvasChange('object:removed', e.target);
                }, e.target.id);
            }
        });

        this.canvas.on('path:created', (e) => {
            console.log('Canvas path:created event', e.path, 'isCollaborating:', this.isCollaborating);
            if (this.isCollaborating && e.path && !e.path.isFromRemote) {
                // Ensure path has an ID
                if (!e.path.id) {
                    e.path.id = 'path_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                }
                console.log('Broadcasting path:created', e.path.id, e.path);
                this.throttledSync(() => {
                    this.broadcastCanvasChange('path:created', e.path);
                }, e.path.id);
            }
        });

        // Track mouse movements for cursor sharing
        this.canvas.on('mouse:move', (e) => {
            if (this.isCollaborating) {
                this.throttledCursorUpdate(e);
            }
        });
    }

    /**
     * Improved throttling with per-object tracking
     */
    throttledSync(callback, objectId = null) {
        const now = Date.now();

        // If we have an object ID, track per-object throttling
        if (objectId) {
            const lastTime = this.pendingBroadcasts.get(objectId) || 0;
            if (now - lastTime > this.syncThrottle) {
                this.pendingBroadcasts.set(objectId, now);
                callback();
            } else {
                // Schedule a delayed broadcast for this object
                setTimeout(() => {
                    if (this.pendingBroadcasts.get(objectId) === lastTime) {
                        this.pendingBroadcasts.set(objectId, Date.now());
                        callback();
                    }
                }, this.syncThrottle - (now - lastTime));
            }
        } else {
            // Global throttling for events without object IDs
            if (now - this.lastSyncTime > this.syncThrottle) {
                this.lastSyncTime = now;
                callback();
            }
        }
    }

    /**
     * Throttled cursor position updates
     */
    throttledCursorUpdate(e) {
        if (!this.lastCursorUpdate || Date.now() - this.lastCursorUpdate > 50) {
            this.lastCursorUpdate = Date.now();
            const pointer = this.canvas.getPointer(e.e);
            this.broadcastCursorPosition(pointer.x, pointer.y);
        }
    }

    /**
     * Broadcast canvas changes to all connected peers
     */
    broadcastCanvasChange(eventType, object) {
        if (!this.isCollaborating) {
            console.log('Not collaborating, skipping broadcast');
            return;
        }

        try {
            const objectData = this.serializeObject(object);
            if (!objectData) {
                console.error('Failed to serialize object for broadcast:', object);
                return;
            }

            const changeData = {
                type: 'canvas-change',
                eventType: eventType,
                objectData: objectData,
                timestamp: Date.now(),
                userId: this.websocket.localUserId
            };

            console.log('Broadcasting canvas change:', eventType, 'objectId:', object.id, 'isCollaborating:', this.isCollaborating);
            const success = this.websocket.broadcast(changeData);
            if (success) {
                console.log('✅ Successfully broadcasted canvas change:', eventType, 'objectId:', object.id);
            } else {
                console.error('❌ Failed to broadcast canvas change:', eventType, 'objectId:', object.id);
            }
        } catch (error) {
            console.error('Error broadcasting canvas change:', error);
        }
    }

    /**
     * Broadcast cursor position to all connected peers
     */
    broadcastCursorPosition(x, y) {
        const cursorData = {
            type: 'cursor-move',
            x: x,
            y: y,
            userId: this.websocket.localUserId,
            userName: this.websocket.localUserName,
            timestamp: Date.now()
        };

        this.websocket.broadcast(cursorData);
    }

    /**
     * Broadcast map change to all connected peers
     */
    broadcastMapChange(mapName) {
        const mapData = {
            type: 'map-change',
            mapName: mapName,
            userId: this.websocket.localUserId,
            userName: this.websocket.localUserName,
            timestamp: Date.now()
        };

        this.websocket.broadcast(mapData);
        console.log('Broadcasted map change to:', mapName);
    }

    /**
     * Handle incoming collaboration data
     */
    handleIncomingData(data, fromPeerId) {
        try {
            switch (data.type) {
                case 'canvas-change':
                    this.handleCanvasChange(data);
                    break;

                case 'cursor-move':
                    this.handleCursorPosition(data);
                    break;

                case 'map-change':
                    this.handleMapChange(data);
                    break;

                case 'user-join':
                    this.handleUserJoin(data, fromPeerId);
                    break;

                case 'user-leave':
                    this.handleUserLeave(data);
                    break;

                case 'canvas-sync-request':
                    this.handleSyncRequest(fromPeerId);
                    break;

                case 'canvas-sync-data':
                    this.handleSyncData(data);
                    break;

                case 'pong':
                    // Handle server pong response (heartbeat)
                    console.log('Received pong from server');
                    break;

                case 'chat-message':
                    this.handleChatMessage(data);
                    break;

                default:
                    console.log('Unknown collaboration data type:', data.type);
            }
        } catch (error) {
            console.error('Error handling incoming data:', error);
        }
    }

    /**
     * Handle canvas changes from remote users
     */
    async handleCanvasChange(data) {
        try {
            console.log('Received canvas change:', data.eventType, data);
            const objectResult = this.deserializeObject(data.objectData);

            // Handle async image loading
            let object;
            if (objectResult instanceof Promise) {
                object = await objectResult;
            } else {
                object = objectResult;
            }

            if (!object) {
                console.log('Failed to deserialize object');
                return;
            }

            // Mark object as from remote to prevent re-broadcasting
            object.isFromRemote = true;

            // Temporarily disable collaboration to prevent echo
            const wasCollaborating = this.isCollaborating;
            this.isCollaborating = false;

            try {
                switch (data.eventType) {
                    case 'object:added':
                    case 'path:created':
                        console.log('Adding remote object to canvas');
                        this.canvas.add(object);
                        break;

                    case 'object:modified':
                        // Find and update existing object
                        const existingObject = this.findObjectById(object.id);
                        if (existingObject) {
                            console.log('Updating existing object');
                            // Mark existing object as remote during update
                            existingObject.isFromRemote = true;
                            existingObject.set(object);
                            existingObject.setCoords();
                            // Preserve z-index for agents
                            if (existingObject.isAgent) {
                                this.canvas.bringToFront(existingObject);
                            }
                        } else {
                            console.log('Object not found for modification, adding it');
                            this.canvas.add(object);
                        }
                        break;

                    case 'object:removed':
                        const objectToRemove = this.findObjectById(object.id);
                        if (objectToRemove) {
                            console.log('Removing remote object from canvas');
                            objectToRemove.isFromRemote = true;
                            this.canvas.remove(objectToRemove);
                        }
                        break;

                    default:
                        console.log('Unknown canvas event type:', data.eventType);
                }
            } finally {
                // Re-enable collaboration after a short delay to ensure events are processed
                setTimeout(() => {
                    this.isCollaborating = wasCollaborating;
                    // Clear isFromRemote flag after processing to allow local modifications
                    if (object && object.isFromRemote) {
                        object.isFromRemote = false;
                    }
                }, 50);
            }

            this.canvas.renderAll();
            console.log('Canvas updated successfully');
        } catch (error) {
            console.error('Error handling canvas change:', error);
        }
    }

    /**
     * Handle cursor position updates from remote users
     */
    handleCursorPosition(data) {
        this.updateUserCursor(data.userId, data.userName, data.x, data.y);
    }

    /**
     * Handle map changes from remote users
     */
    handleMapChange(data) {
        console.log('Received map change from', data.userName, ':', data.mapName);

        // Update the map selector without triggering the change event
        const mapSelect = document.getElementById('map-select');
        if (mapSelect && mapSelect.value !== data.mapName) {
            // Temporarily remove the event listener to prevent infinite loop
            const originalHandler = mapSelect.onchange;
            mapSelect.onchange = null;

            // Update the dropdown value
            mapSelect.value = data.mapName;

            // Update the global current map variable
            if (window.currentMap !== undefined) {
                window.currentMap = data.mapName;
            }

            // Load the new map
            if (window.loadMap && typeof window.loadMap === 'function') {
                window.loadMap(data.mapName);
            }

            // Restore the event listener
            setTimeout(() => {
                mapSelect.onchange = originalHandler;
            }, 100);

            console.log('Map synchronized to:', data.mapName);
        }
    }

    /**
     * Handle user join events
     */
    handleUserJoin(data, fromClientId) {
        console.log('User joined:', data.userName);
        this.addUser(data.userId, data.userName, fromClientId);

        // If we're the host, send current canvas state and map to new user
        if (this.websocket.isHost) {
            this.sendCanvasSync();
            this.sendMapSync();
        } else {
            // If we're not the host, request canvas sync
            this.requestCanvasSync();
        }
    }

    /**
     * Handle user leave events
     */
    handleUserLeave(data) {
        console.log('User left:', data.userName);
        this.removeUser(data.userId);
    }

    /**
     * Send current canvas state to all peers
     */
    sendCanvasSync() {
        try {
            const canvasData = JSON.stringify(this.canvas.toJSON());
            const syncData = {
                type: 'canvas-sync-data',
                canvasData: canvasData,
                timestamp: Date.now()
            };

            this.websocket.broadcast(syncData);
            console.log('Sent canvas sync to all peers');
        } catch (error) {
            console.error('Error sending canvas sync:', error);
        }
    }

    /**
     * Send current map state to all peers
     */
    sendMapSync() {
        try {
            const currentMap = window.currentMap || document.getElementById('map-select')?.value || 'Ascent';
            const mapSyncData = {
                type: 'map-change',
                mapName: currentMap,
                userId: this.websocket.localUserId,
                userName: this.websocket.localUserName,
                timestamp: Date.now(),
                isSync: true // Flag to indicate this is a sync, not a user-initiated change
            };

            this.websocket.broadcast(mapSyncData);
            console.log('Sent map sync to all peers. Map:', currentMap);
        } catch (error) {
            console.error('Error sending map sync:', error);
        }
    }

    /**
     * Request canvas sync from host
     */
    requestCanvasSync() {
        const syncRequest = {
            type: 'canvas-sync-request',
            userId: this.websocket.localUserId,
            timestamp: Date.now()
        };

        this.websocket.broadcast(syncRequest);
        console.log('Requested canvas sync');
    }

    /**
     * Handle canvas sync requests (host only)
     */
    handleSyncRequest(fromClientId) {
        if (this.websocket.isHost) {
            this.sendCanvasSync();
        }
    }

    /**
     * Handle incoming canvas sync data
     */
    handleSyncData(data) {
        try {
            this.canvas.isLoadingFromJSON = true;
            this.canvas.loadFromJSON(data.canvasData, () => {
                this.canvas.isLoadingFromJSON = false;
                this.canvas.renderAll();
                console.log('Canvas synced successfully');
            });
        } catch (error) {
            console.error('Error syncing canvas:', error);
            this.canvas.isLoadingFromJSON = false;
        }
    }

    /**
     * Serialize a Fabric.js object for transmission
     */
    serializeObject(object) {
        try {
            // Add unique ID if not present
            if (!object.id) {
                object.id = 'obj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            const serialized = object.toObject(['id', 'isAgent', 'isDrawingPath', 'name', 'isCustomImage']);

            // For ALL images (not just agents), include the source URL and important properties
            if (object.type === 'image' && object._element) {
                serialized.src = object._element.src || object.getSrc();
                // Include all transformation properties for proper synchronization
                serialized.scaleX = object.scaleX;
                serialized.scaleY = object.scaleY;
                serialized.left = object.left;
                serialized.top = object.top;
                serialized.angle = object.angle;
                serialized.flipX = object.flipX;
                serialized.flipY = object.flipY;
                serialized.originX = object.originX;
                serialized.originY = object.originY;
                serialized.width = object.width;
                serialized.height = object.height;

                // Include custom image flag
                if (object.isCustomImage) {
                    serialized.isCustomImage = true;
                }
            }

            return serialized;
        } catch (error) {
            console.error('Error serializing object:', error);
            return null;
        }
    }

    /**
     * Deserialize object data back to Fabric.js object
     */
    deserializeObject(objectData) {
        try {
            // Create object based on type
            let object;
            switch (objectData.type) {
                case 'image':
                    // Handle ALL images (agents and custom images)
                    if (objectData.src) {
                        return new Promise((resolve) => {
                            fabric.Image.fromURL(objectData.src, (img) => {
                                if (img) {
                                    // Set all properties from the serialized data
                                    img.set(objectData);
                                    img.isFromRemote = true;
                                    resolve(img);
                                } else {
                                    console.error('Failed to load image from URL:', objectData.src);
                                    resolve(null);
                                }
                            }, { crossOrigin: 'Anonymous' });
                        });
                    } else {
                        console.warn('Image object missing src property:', objectData);
                        return null;
                    }

                case 'path':
                    object = new fabric.Path(objectData.path, objectData);
                    break;

                case 'line':
                    object = new fabric.Line([objectData.x1, objectData.y1, objectData.x2, objectData.y2], objectData);
                    break;

                case 'rect':
                    object = new fabric.Rect(objectData);
                    break;

                case 'circle':
                    object = new fabric.Circle(objectData);
                    break;

                case 'triangle':
                    object = new fabric.Triangle(objectData);
                    break;

                case 'group':
                    // Handle groups recursively
                    const objects = objectData.objects.map(obj => this.deserializeObject(obj)).filter(Boolean);
                    object = new fabric.Group(objects, objectData);
                    break;

                default:
                    console.warn('Unknown object type:', objectData.type);
                    return null;
            }

            return object;
        } catch (error) {
            console.error('Error deserializing object:', error);
            return null;
        }
    }

    /**
     * Find object by ID
     */
    findObjectById(id) {
        return this.canvas.getObjects().find(obj => obj.id === id);
    }

    /**
     * Add a connected user
     */
    addUser(userId, userName, clientId) {
        this.connectedUsers.set(userId, {
            userName: userName,
            clientId: clientId,
            joinTime: Date.now()
        });
        this.updateUsersDisplay();
    }

    /**
     * Remove a connected user
     */
    removeUser(userId) {
        this.connectedUsers.delete(userId);
        this.removeUserCursor(userId);
        this.updateUsersDisplay();
    }

    /**
     * Update user cursor position
     */
    updateUserCursor(userId, userName, x, y) {
        // Implementation for showing user cursors would go here
        // This is a placeholder for the cursor visualization
        console.log(`User ${userName} cursor at (${x}, ${y})`);
    }

    /**
     * Remove user cursor
     */
    removeUserCursor(userId) {
        this.userCursors.delete(userId);
    }

    /**
     * Update the users display panel
     */
    updateUsersDisplay() {
        const usersContainer = document.getElementById('connected-users');
        if (!usersContainer) return;

        usersContainer.innerHTML = '';

        // Add local user
        const localUserDiv = document.createElement('div');
        localUserDiv.className = 'user-item local-user';
        localUserDiv.innerHTML = `
            <span class="user-indicator"></span>
            <span class="user-name">${this.websocket.localUserName} (You)</span>
        `;
        usersContainer.appendChild(localUserDiv);

        // Add connected users
        this.connectedUsers.forEach((user, userId) => {
            const userDiv = document.createElement('div');
            userDiv.className = 'user-item';
            userDiv.innerHTML = `
                <span class="user-indicator"></span>
                <span class="user-name">${user.userName}</span>
            `;
            usersContainer.appendChild(userDiv);
        });
    }

    /**
     * Show the users panel
     */
    showUsersPanel() {
        const usersPanel = document.getElementById('users-panel');
        if (usersPanel) {
            usersPanel.style.display = 'block';
            this.updateUsersDisplay();
        }
    }

    /**
     * Hide the users panel
     */
    hideUsersPanel() {
        const usersPanel = document.getElementById('users-panel');
        if (usersPanel) {
            usersPanel.style.display = 'none';
        }
    }

    /**
     * Update connection status display
     */
    updateConnectionStatus(status) {
        console.log('Updating connection status to:', status);
        const statusElement = document.getElementById('connection-status');
        const statusText = document.querySelector('#connection-status .status-text');

        if (statusElement && statusText) {
            console.log('Found status elements, updating...');
            statusElement.className = `connection-status ${status}`;

            switch (status) {
                case 'connected':
                    statusText.textContent = `Connected (${this.connectedUsers.size + 1} users)`;
                    console.log('Set status text to connected');
                    break;
                case 'connecting':
                    statusText.textContent = 'Connecting...';
                    break;
                case 'offline':
                    statusText.textContent = 'Offline';
                    console.log('Set status text to offline');
                    break;
                case 'error':
                    statusText.textContent = 'Connection Error';
                    break;
                default:
                    statusText.textContent = 'Unknown';
            }
        } else {
            console.error('Could not find status elements:', { statusElement, statusText });
        }
    }

    /**
     * Show the chat panel
     */
    showChatPanel() {
        const chatPanel = document.getElementById('chat-panel');
        if (chatPanel) {
            chatPanel.style.display = 'block';
        }
    }

    /**
     * Hide the chat panel
     */
    hideChatPanel() {
        const chatPanel = document.getElementById('chat-panel');
        if (chatPanel) {
            chatPanel.style.display = 'none';
        }
    }

    /**
     * Set up chat event handlers
     */
    setupChatHandlers() {
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-chat');

        if (chatInput && sendButton) {
            // Send message on button click
            sendButton.addEventListener('click', () => {
                this.sendChatMessage();
            });

            // Send message on Enter key
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendChatMessage();
                }
            });
        }
    }

    /**
     * Send a chat message
     */
    sendChatMessage() {
        const chatInput = document.getElementById('chat-input');
        const message = chatInput.value.trim();

        if (message && this.isCollaborating) {
            const chatData = {
                type: 'chat-message',
                message: message,
                userId: this.websocket.localUserId,
                userName: this.websocket.localUserName,
                timestamp: new Date().toISOString()
            };

            // Send to server
            this.websocket.broadcast(chatData);

            // Add to local chat
            this.addChatMessage(chatData, true);

            // Clear input
            chatInput.value = '';
        }
    }

    /**
     * Handle incoming chat message
     */
    handleChatMessage(data) {
        this.addChatMessage(data, false);
    }

    /**
     * Add a chat message to the display
     */
    addChatMessage(data, isOwn) {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${isOwn ? 'own' : 'other'}`;

        const timestamp = new Date(data.timestamp).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="sender">${data.userName}</div>
            <div class="content">${this.escapeHtml(data.message)}</div>
            <div class="timestamp">${timestamp}</div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Disconnect from collaboration
     */
    disconnect() {
        this.isCollaborating = false;
        this.connectedUsers.clear();
        this.userCursors.clear();
        this.websocket.disconnect();
        this.updateConnectionStatus('offline');
        this.hideUsersPanel();
        this.hideChatPanel();
        console.log('Disconnected from collaboration');
    }
}

// Export for use in other files
window.CollaborationManager = CollaborationManager;
