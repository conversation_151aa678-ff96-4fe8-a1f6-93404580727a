/**
 * Supabase Client for Valorant Strategy Board
 * Handles real-time database operations and synchronization
 */

class SupabaseManager {
    constructor() {
        this.supabase = null;
        this.currentSession = null;
        this.currentUser = null;
        this.realtimeChannel = null;
        this.isInitialized = false;
        
        // Event callbacks
        this.onSessionJoined = null;
        this.onSessionLeft = null;
        this.onCanvasObjectChanged = null;
        this.onUserJoined = null;
        this.onUserLeft = null;
        this.onCursorMoved = null;
        this.onChatMessage = null;
        this.onMapChanged = null;
        this.onError = null;
        
        console.log('SupabaseManager initialized');
    }
    
    /**
     * Initialize Supabase client
     */
    async initialize(supabaseUrl, supabaseKey) {
        try {
            if (!window.supabase) {
                throw new Error('Supabase library not loaded');
            }
            
            this.supabase = window.supabase.createClient(supabaseUrl, supabaseKey, {
                realtime: {
                    params: {
                        eventsPerSecond: 10
                    }
                }
            });
            
            this.isInitialized = true;
            console.log('Supabase client initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize Supabase:', error);
            if (this.onError) this.onError(error);
            return false;
        }
    }
    
    /**
     * Generate a unique user ID
     */
    generateUserId() {
        return 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36);
    }
    
    /**
     * Create a new collaboration session
     */
    async createSession(sessionName, userName) {
        try {
            if (!this.isInitialized) {
                throw new Error('Supabase not initialized');
            }
            
            this.currentUser = {
                id: this.generateUserId(),
                name: userName || 'Host_' + Math.random().toString(36).substr(2, 6)
            };
            
            // Generate session code
            const { data: codeData, error: codeError } = await this.supabase
                .rpc('generate_session_code');
                
            if (codeError) throw codeError;
            const sessionCode = codeData;
            
            // Create session
            const { data: sessionData, error: sessionError } = await this.supabase
                .from('sessions')
                .insert({
                    session_code: sessionCode,
                    session_name: sessionName,
                    host_user_id: this.currentUser.id,
                    current_map: 'Ascent',
                    canvas_data: {}
                })
                .select()
                .single();
                
            if (sessionError) throw sessionError;
            
            this.currentSession = sessionData;
            
            // Add user to session
            await this.addUserToSession(true);
            
            // Set up real-time subscriptions
            await this.setupRealtimeSubscriptions();
            
            console.log('Session created successfully:', sessionCode);
            return {
                sessionCode: sessionCode,
                sessionId: sessionData.id
            };
            
        } catch (error) {
            console.error('Failed to create session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Join an existing session
     */
    async joinSession(sessionCode, userName) {
        try {
            if (!this.isInitialized) {
                throw new Error('Supabase not initialized');
            }
            
            this.currentUser = {
                id: this.generateUserId(),
                name: userName || 'User_' + Math.random().toString(36).substr(2, 6)
            };
            
            // Find session by code
            const { data: sessionData, error: sessionError } = await this.supabase
                .from('sessions')
                .select('*')
                .eq('session_code', sessionCode.toUpperCase())
                .eq('is_active', true)
                .single();
                
            if (sessionError || !sessionData) {
                throw new Error('Session not found or inactive');
            }
            
            this.currentSession = sessionData;
            
            // Add user to session
            await this.addUserToSession(false);
            
            // Set up real-time subscriptions
            await this.setupRealtimeSubscriptions();
            
            // Load current canvas state
            await this.loadCanvasState();
            
            console.log('Joined session successfully:', sessionCode);
            return {
                sessionId: sessionData.id,
                currentMap: sessionData.current_map,
                canvasData: sessionData.canvas_data
            };
            
        } catch (error) {
            console.error('Failed to join session:', error);
            if (this.onError) this.onError(error);
            throw error;
        }
    }
    
    /**
     * Add current user to session
     */
    async addUserToSession(isHost) {
        try {
            const { error } = await this.supabase
                .from('session_users')
                .insert({
                    session_id: this.currentSession.id,
                    user_id: this.currentUser.id,
                    user_name: this.currentUser.name,
                    is_host: isHost,
                    is_online: true
                });
                
            if (error) throw error;
            
            console.log('User added to session:', this.currentUser.name);
        } catch (error) {
            console.error('Failed to add user to session:', error);
            throw error;
        }
    }
    
    /**
     * Set up real-time subscriptions
     */
    async setupRealtimeSubscriptions() {
        try {
            if (this.realtimeChannel) {
                await this.supabase.removeChannel(this.realtimeChannel);
            }
            
            this.realtimeChannel = this.supabase
                .channel(`session_${this.currentSession.id}`)
                .on('postgres_changes', {
                    event: '*',
                    schema: 'public',
                    table: 'canvas_objects',
                    filter: `session_id=eq.${this.currentSession.id}`
                }, (payload) => {
                    this.handleCanvasObjectChange(payload);
                })
                .on('postgres_changes', {
                    event: '*',
                    schema: 'public',
                    table: 'session_users',
                    filter: `session_id=eq.${this.currentSession.id}`
                }, (payload) => {
                    this.handleUserChange(payload);
                })
                .on('postgres_changes', {
                    event: '*',
                    schema: 'public',
                    table: 'user_cursors',
                    filter: `session_id=eq.${this.currentSession.id}`
                }, (payload) => {
                    this.handleCursorChange(payload);
                })
                .on('postgres_changes', {
                    event: '*',
                    schema: 'public',
                    table: 'chat_messages',
                    filter: `session_id=eq.${this.currentSession.id}`
                }, (payload) => {
                    this.handleChatMessage(payload);
                })
                .on('postgres_changes', {
                    event: 'UPDATE',
                    schema: 'public',
                    table: 'sessions',
                    filter: `id=eq.${this.currentSession.id}`
                }, (payload) => {
                    this.handleSessionChange(payload);
                })
                .subscribe();
                
            console.log('Real-time subscriptions set up successfully');
        } catch (error) {
            console.error('Failed to set up real-time subscriptions:', error);
            throw error;
        }
    }
    
    /**
     * Handle canvas object changes from real-time subscription
     */
    handleCanvasObjectChange(payload) {
        console.log('Canvas object change received:', payload);
        
        if (payload.new && payload.new.created_by !== this.currentUser.id) {
            if (this.onCanvasObjectChanged) {
                this.onCanvasObjectChanged(payload);
            }
        }
    }
    
    /**
     * Handle user changes from real-time subscription
     */
    handleUserChange(payload) {
        console.log('User change received:', payload);
        
        if (payload.eventType === 'INSERT' && this.onUserJoined) {
            this.onUserJoined(payload.new);
        } else if (payload.eventType === 'DELETE' && this.onUserLeft) {
            this.onUserLeft(payload.old);
        }
    }
    
    /**
     * Handle cursor changes from real-time subscription
     */
    handleCursorChange(payload) {
        if (payload.new && payload.new.user_id !== this.currentUser.id && this.onCursorMoved) {
            this.onCursorMoved(payload.new);
        }
    }
    
    /**
     * Handle chat messages from real-time subscription
     */
    handleChatMessage(payload) {
        if (payload.new && payload.new.user_id !== this.currentUser.id && this.onChatMessage) {
            this.onChatMessage(payload.new);
        }
    }
    
    /**
     * Handle session changes (like map changes)
     */
    handleSessionChange(payload) {
        console.log('Session change received:', payload);

        if (payload.new && this.onMapChanged) {
            const oldMap = payload.old?.current_map;
            const newMap = payload.new.current_map;

            if (oldMap !== newMap) {
                this.onMapChanged(newMap);
            }
        }
    }

    /**
     * Add or update a canvas object
     */
    async addCanvasObject(objectId, objectType, objectData) {
        try {
            const { error } = await this.supabase
                .from('canvas_objects')
                .upsert({
                    session_id: this.currentSession.id,
                    object_id: objectId,
                    object_type: objectType,
                    object_data: objectData,
                    created_by: this.currentUser.id,
                    is_deleted: false
                });

            if (error) throw error;
            console.log('Canvas object added/updated:', objectId);
        } catch (error) {
            console.error('Failed to add canvas object:', error);
            throw error;
        }
    }

    /**
     * Remove a canvas object
     */
    async removeCanvasObject(objectId) {
        try {
            const { error } = await this.supabase
                .from('canvas_objects')
                .update({ is_deleted: true })
                .eq('session_id', this.currentSession.id)
                .eq('object_id', objectId);

            if (error) throw error;
            console.log('Canvas object removed:', objectId);
        } catch (error) {
            console.error('Failed to remove canvas object:', error);
            throw error;
        }
    }

    /**
     * Update map for the session
     */
    async updateMap(mapName) {
        try {
            const { error } = await this.supabase
                .from('sessions')
                .update({ current_map: mapName })
                .eq('id', this.currentSession.id);

            if (error) throw error;
            console.log('Map updated:', mapName);
        } catch (error) {
            console.error('Failed to update map:', error);
            throw error;
        }
    }

    /**
     * Update user cursor position
     */
    async updateCursor(x, y) {
        try {
            const { error } = await this.supabase
                .from('user_cursors')
                .upsert({
                    session_id: this.currentSession.id,
                    user_id: this.currentUser.id,
                    user_name: this.currentUser.name,
                    x: x,
                    y: y
                });

            if (error) throw error;
        } catch (error) {
            console.error('Failed to update cursor:', error);
        }
    }

    /**
     * Send a chat message
     */
    async sendChatMessage(message) {
        try {
            const { error } = await this.supabase
                .from('chat_messages')
                .insert({
                    session_id: this.currentSession.id,
                    user_id: this.currentUser.id,
                    user_name: this.currentUser.name,
                    message: message
                });

            if (error) throw error;
            console.log('Chat message sent:', message);
        } catch (error) {
            console.error('Failed to send chat message:', error);
            throw error;
        }
    }

    /**
     * Load current canvas state
     */
    async loadCanvasState() {
        try {
            const { data, error } = await this.supabase
                .from('canvas_objects')
                .select('*')
                .eq('session_id', this.currentSession.id)
                .eq('is_deleted', false)
                .order('created_at', { ascending: true });

            if (error) throw error;

            console.log('Canvas state loaded:', data?.length || 0, 'objects');
            return data || [];
        } catch (error) {
            console.error('Failed to load canvas state:', error);
            throw error;
        }
    }

    /**
     * Get session users
     */
    async getSessionUsers() {
        try {
            const { data, error } = await this.supabase
                .from('session_users')
                .select('*')
                .eq('session_id', this.currentSession.id)
                .eq('is_online', true);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Failed to get session users:', error);
            throw error;
        }
    }

    /**
     * Leave the current session
     */
    async leaveSession() {
        try {
            if (!this.currentSession) return;

            // Mark user as offline
            await this.supabase
                .from('session_users')
                .update({ is_online: false })
                .eq('session_id', this.currentSession.id)
                .eq('user_id', this.currentUser.id);

            // Remove real-time subscriptions
            if (this.realtimeChannel) {
                await this.supabase.removeChannel(this.realtimeChannel);
                this.realtimeChannel = null;
            }

            this.currentSession = null;
            this.currentUser = null;

            console.log('Left session successfully');
        } catch (error) {
            console.error('Failed to leave session:', error);
            throw error;
        }
    }

    /**
     * Check if currently in a session
     */
    isInSession() {
        return this.currentSession !== null;
    }

    /**
     * Get current session info
     */
    getCurrentSession() {
        return this.currentSession;
    }

    /**
     * Get current user info
     */
    getCurrentUser() {
        return this.currentUser;
    }
}

// Export for use in other files
window.SupabaseManager = SupabaseManager;
