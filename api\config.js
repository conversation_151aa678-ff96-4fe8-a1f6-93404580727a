/**
 * Vercel API endpoint to provide Supabase configuration
 * This allows the frontend to access environment variables securely
 */

export default function handler(req, res) {
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
        res.status(200).end();
        return;
    }
    
    // Only allow GET requests
    if (req.method !== 'GET') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }
    
    try {
        // Get environment variables
        const supabaseUrl = process.env.SUPABASE_URL;
        const supabaseKey = process.env.SUPABASE_ANON_KEY;
        
        // Check if environment variables are set
        if (!supabaseUrl || !supabaseKey) {
            res.status(500).json({ 
                error: 'Supabase configuration not found',
                message: 'Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables'
            });
            return;
        }
        
        // Return configuration
        res.status(200).json({
            supabaseUrl: supabaseUrl,
            supabaseKey: supabaseKey,
            timestamp: new Date().toISOString()
        });
        
    } catch (error) {
        console.error('Error in config API:', error);
        res.status(500).json({ 
            error: 'Internal server error',
            message: 'Failed to load configuration'
        });
    }
}
