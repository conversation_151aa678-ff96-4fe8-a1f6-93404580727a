<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Valorant Map Viewer</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #0F1923;
            color: white;
            margin: 0;
            padding: 20px;
        }
        
        h1 {
            color: #FF4655;
        }
        
        .container {
            display: flex;
            flex-direction: column;
            height: 90vh;
        }
        
        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 10px;
        }
        
        .canvas-container {
            flex: 1;
            background-color: #1F2731;
            position: relative;
            border-radius: 5px;
            overflow: hidden;
        }
        
        canvas {
            width: 100%;
            height: 100%;
            display: block;
        }
        
        select, button {
            background-color: #364966;
            color: white;
            border: 1px solid #5c6f91;
            padding: 5px 10px;
            border-radius: 3px;
        }
        
        select:focus, button:focus {
            outline: none;
            border-color: #FF4655;
        }
        
        button:hover {
            background-color: #465a7d;
        }
        
        .log {
            background-color: black;
            color: #00ff00;
            font-family: monospace;
            padding: 10px;
            height: 150px;
            overflow-y: auto;
            margin-top: 20px;
            border-radius: 5px;
        }
        
        .error {
            color: #ff5555;
        }
    </style>
</head>
<body>
    <h1>Simple Valorant Map Viewer</h1>
    
    <div class="container">
        <div class="controls">
            <select id="map-select">
                <option value="Haven">Haven</option>
                <option value="Bind">Bind</option>
                <option value="Ascent">Ascent</option>
                <option value="Split">Split</option>
                <option value="Icebox">Icebox</option>
                <option value="Pearl">Pearl</option>
                <option value="Fracture">Fracture</option>
                <option value="Lotus">Lotus</option>
                <option value="Sunset">Sunset</option>
                <option value="Abyss">Abyss</option>
            </select>
            
            <button id="load-btn">Load Map</button>
            <button id="test-all-paths">Test All Possible Paths</button>
        </div>
        
        <div class="canvas-container">
            <canvas id="map-canvas"></canvas>
        </div>
        
        <div class="log" id="log-container"></div>
    </div>
    
    <script>
        // DOM Elements
        const mapSelect = document.getElementById('map-select');
        const loadBtn = document.getElementById('load-btn');
        const testAllPathsBtn = document.getElementById('test-all-paths');
        const logContainer = document.getElementById('log-container');
        
        // Create Fabric Canvas
        const canvas = new fabric.Canvas('map-canvas', {
            width: window.innerWidth - 50,
            height: window.innerHeight - 250
        });
        
        // Possible paths to test
        const possiblePaths = [
            './mini-map/',
            '../mini-map/',
            '/mini-map/',
            '/Strategy-board/mini-map/',
            window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1) + 'mini-map/',
            window.location.origin + window.location.pathname.substring(0, window.location.pathname.lastIndexOf('/') + 1) + 'mini-map/'
        ];
        
        // Log helper function
        function log(message, isError = false) {
            const entry = document.createElement('div');
            entry.textContent = message;
            if (isError) {
                entry.classList.add('error');
            }
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }
        
        // Load map function
        function loadMap(mapName, basePath) {
            log(`Attempting to load map: ${mapName} from path: ${basePath}`);
            
            // Format the map filename based on the map name
            let formattedMapName;
            
            switch(mapName) {
                case 'Ascent':
                    formattedMapName = 'Ascent mini map.png';
                    break;
                case 'Bind':
                    formattedMapName = 'Bind mini map.png';
                    break;
                case 'Haven':
                    formattedMapName = 'haven mini map.png';
                    break;
                case 'Split':
                    formattedMapName = 'Split mini map.png';
                    break;
                case 'Icebox':
                    formattedMapName = 'ice box mini map.png';
                    break;
                case 'Pearl':
                    formattedMapName = 'Pearl mini map.png';
                    break;
                case 'Fracture':
                    formattedMapName = 'fracture mini map.png';
                    break;
                case 'Lotus':
                    formattedMapName = 'lotus mini map.png';
                    break;
                case 'Sunset':
                    formattedMapName = 'sunsetmini map.png';
                    break;
                case 'Abyss':
                    formattedMapName = 'abyss mini map.png';
                    break;
                default:
                    formattedMapName = 'haven mini map.png';
            }
            
            const fullPath = basePath + formattedMapName;
            log(`Full path: ${fullPath}`);
            
            // Create image element to test loading
            const imgElement = new Image();
            
            imgElement.onload = function() {
                log(`✅ Successfully loaded map image! Size: ${imgElement.width}x${imgElement.height}`);
                
                // Create fabric image
                const fabricImage = new fabric.Image(imgElement);
                
                // Scale to fit canvas
                const scaleX = canvas.width / imgElement.width;
                const scaleY = canvas.height / imgElement.height;
                const scale = Math.min(scaleX, scaleY) * 0.9;
                
                fabricImage.scale(scale);
                
                // Clear canvas and set background image
                canvas.clear();
                canvas.setBackgroundImage(fabricImage, canvas.renderAll.bind(canvas), {
                    originX: 'center',
                    originY: 'center',
                    left: canvas.width / 2,
                    top: canvas.height / 2
                });
                
                // Add a text overlay to confirm map name
                const text = new fabric.Text(`Map: ${mapName}`, {
                    left: 20,
                    top: 20,
                    fill: 'white',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    padding: 10
                });
                
                canvas.add(text);
                canvas.renderAll();
            };
            
            imgElement.onerror = function() {
                log(`❌ Failed to load map image from: ${fullPath}`, true);
                
                // Display error on canvas
                canvas.clear();
                const text = new fabric.Text(`Failed to load map: ${mapName}\nPath: ${fullPath}`, {
                    left: canvas.width / 2,
                    top: canvas.height / 2,
                    originX: 'center',
                    originY: 'center',
                    textAlign: 'center',
                    fill: 'red',
                    fontSize: 20
                });
                
                canvas.add(text);
                canvas.renderAll();
            };
            
            // Set source to trigger loading
            imgElement.src = fullPath;
        }
        
        // Test all possible paths for a map
        function testAllPaths() {
            log("Testing all possible paths for map loading...");
            
            const mapName = mapSelect.value;
            
            // Try each path
            possiblePaths.forEach((path, index) => {
                setTimeout(() => {
                    log(`Testing path ${index + 1}: ${path}`);
                    loadMap(mapName, path);
                }, index * 2000); // 2 second delay between attempts
            });
        }
        
        // Event listeners
        loadBtn.addEventListener('click', function() {
            const mapName = mapSelect.value;
            // Try with the default path
            loadMap(mapName, './mini-map/');
        });
        
        testAllPathsBtn.addEventListener('click', testAllPaths);
        
        // Initial setup - resize canvas on window resize
        window.addEventListener('resize', function() {
            canvas.setWidth(window.innerWidth - 50);
            canvas.setHeight(window.innerHeight - 250);
            canvas.renderAll();
        });
        
        // Initial log
        log("Simple Valorant Map Viewer initialized.");
    </script>
</body>
</html> 