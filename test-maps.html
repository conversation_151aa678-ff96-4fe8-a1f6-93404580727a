<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Map Loading</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #0F1923;
            color: white;
            margin: 20px;
        }
        
        h1 {
            color: #FF4655;
        }
        
        .map-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .map-item {
            border: 1px solid #999;
            padding: 10px;
            border-radius: 5px;
            width: 300px;
        }
        
        .map-item img {
            max-width: 100%;
            height: auto;
            margin-top: 10px;
            border: 2px solid #333;
        }
        
        .map-item h3 {
            margin-top: 0;
        }
        
        .status {
            margin-top: 5px;
            font-weight: bold;
        }
        
        .success {
            color: #00ff00;
        }
        
        .error {
            color: #ff0000;
        }
    </style>
</head>
<body>
    <h1>Valorant Map Loading Test</h1>
    <p>This page tests if the map images can be loaded directly. If you see the maps displayed below, then the paths are correct.</p>
    
    <div class="map-container" id="map-container">
        <!-- Maps will be inserted here by JavaScript -->
    </div>
    
    <h2>Test Log:</h2>
    <div id="log" style="background: #000; padding: 10px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
    
    <script>
        // Log function
        function log(message, isError = false) {
            const logElement = document.getElementById('log');
            const entry = document.createElement('div');
            entry.textContent = message;
            if (isError) {
                entry.style.color = 'red';
            }
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        // Maps to test
        const maps = [
            { name: 'Haven', filename: 'haven mini map.png' },
            { name: 'Bind', filename: 'Bind mini map.png' },
            { name: 'Ascent', filename: 'Ascent mini map.png' },
            { name: 'Split', filename: 'Split mini map.png' },
            { name: 'Icebox', filename: 'ice box mini map.png' },
            { name: 'Pearl', filename: 'Pearl mini map.png' },
            { name: 'Fracture', filename: 'fracture mini map.png' },
            { name: 'Lotus', filename: 'lotus mini map.png' },
            { name: 'Sunset', filename: 'sunsetmini map.png' },
            { name: 'Abyss', filename: 'abyss mini map.png' }
        ];
        
        // Possible base paths to test
        const basePaths = [
            './mini-map/',
            '../mini-map/',
            '/mini-map/',
            '/Strategy-board/mini-map/',
            window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1) + 'mini-map/'
        ];
        
        // Create map elements
        const mapContainer = document.getElementById('map-container');
        
        basePaths.forEach(basePath => {
            log(`Testing base path: ${basePath}`);
            
            const pathHeader = document.createElement('h2');
            pathHeader.textContent = `Path: ${basePath}`;
            pathHeader.style.width = '100%';
            pathHeader.style.marginTop = '30px';
            mapContainer.appendChild(pathHeader);
            
            maps.forEach(map => {
                const mapItem = document.createElement('div');
                mapItem.className = 'map-item';
                
                const mapTitle = document.createElement('h3');
                mapTitle.textContent = map.name;
                mapItem.appendChild(mapTitle);
                
                const mapPath = document.createElement('div');
                mapPath.textContent = basePath + map.filename;
                mapItem.appendChild(mapPath);
                
                const mapImage = document.createElement('img');
                mapImage.alt = map.name;
                
                const status = document.createElement('div');
                status.className = 'status';
                mapItem.appendChild(status);
                
                mapImage.onload = function() {
                    status.textContent = 'Loaded successfully';
                    status.classList.add('success');
                    log(`✅ ${map.name} loaded from ${basePath}`);
                };
                
                mapImage.onerror = function() {
                    status.textContent = 'Failed to load';
                    status.classList.add('error');
                    log(`❌ ${map.name} failed to load from ${basePath}`, true);
                };
                
                mapImage.src = basePath + map.filename;
                mapItem.appendChild(mapImage);
                
                mapContainer.appendChild(mapItem);
            });
        });
    </script>
</body>
</html> 