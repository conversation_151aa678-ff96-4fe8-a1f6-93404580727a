@echo off
echo 🎯 Setting up Valorant Strategy Board for local development...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Python is not installed. You'll need to use another HTTP server.
)

echo ✅ Prerequisites check complete

REM Create .env.local from example if it doesn't exist
if not exist ".env.local" (
    echo 📝 Creating .env.local from example...
    copy ".env.local.example" ".env.local"
    echo ✅ .env.local created. Edit it with your credentials if needed.
) else (
    echo ✅ .env.local already exists
)

echo.
echo 🚀 Setup complete! Choose how to run the app:
echo.
echo Option 1 - Simple HTTP Server (Recommended):
echo   python -m http.server 8000
echo.
echo Option 2 - Using npm:
echo   npm run local
echo.
echo Option 3 - Using Vercel (for testing API routes):
echo   npm install -g vercel
echo   npm run dev
echo.
echo Then open: http://localhost:8000
echo.
echo 📋 Next steps:
echo 1. Make sure you've run the SQL setup script in your Supabase project
echo 2. Start the local server using one of the options above
echo 3. Open the app and test collaboration features
echo.
echo 🔧 Troubleshooting:
echo - If you see a configuration modal, your local config isn't loading
echo - Check that js/local-config.js has the correct credentials
echo - Make sure you're using an HTTP server, not opening the file directly
echo.
pause
