/**
 * Build script to create production-ready files
 * This script removes development-specific configurations
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Building production version...');

// Read the development index.html
const indexPath = path.join(__dirname, 'index.html');
let indexContent = fs.readFileSync(indexPath, 'utf8');

// Remove the local config script line for production
indexContent = indexContent.replace(
    /\s*<!-- Local development configuration \(only for development\) -->\s*\n\s*<script src="js\/local-config\.js"><\/script>\s*\n/,
    '\n'
);

// Write the production version
const prodIndexPath = path.join(__dirname, 'index.prod.html');
fs.writeFileSync(prodIndexPath, indexContent);

console.log('✅ Production index.html created as index.prod.html');
console.log('📝 For deployment:');
console.log('   1. Rename index.prod.html to index.html');
console.log('   2. Remove js/local-config.js from deployment');
console.log('   3. Set environment variables in your hosting platform');

// Create a deployment checklist
const checklist = `
# 🚀 Production Deployment Checklist

## Files to Deploy:
✅ index.html (use index.prod.html)
✅ css/
✅ js/ (except js/local-config.js)
✅ agent-icons/
✅ mini-map/
✅ api/
✅ package.json
✅ vercel.json
✅ supabase-setup.sql (for reference)

## Files to EXCLUDE:
❌ js/local-config.js
❌ .env.local
❌ .env.local.example
❌ build-production.js
❌ server/ (not needed for Vercel)

## Environment Variables to Set:
🔑 SUPABASE_URL=https://cnpireeeqotnmbrctglr.supabase.co
🔑 SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.q-LoWCfeXOuAAwhEhJGH_4RoiaCXMPS0S9TONU75Bew
`;

fs.writeFileSync(path.join(__dirname, 'DEPLOYMENT-CHECKLIST.md'), checklist);
console.log('📋 Deployment checklist created: DEPLOYMENT-CHECKLIST.md');
