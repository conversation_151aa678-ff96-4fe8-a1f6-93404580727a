/**
 * Supabase-based Collaboration Manager for Valorant Strategy Board
 * Replaces WebSocket-based collaboration with Supabase real-time database
 */

class SupabaseCollaborationManager {
    constructor(canvas) {
        this.canvas = canvas;
        this.supabaseManager = null;
        this.isCollaborating = false;
        this.connectedUsers = new Map();
        this.userCursors = new Map();
        this.lastSyncTime = 0;
        this.syncThrottle = 100; // Throttle for better performance
        this.pendingUpdates = new Map();
        
        console.log('SupabaseCollaborationManager initialized');
    }
    
    /**
     * Initialize with Supabase credentials
     */
    async initialize(supabaseUrl, supabaseKey) {
        try {
            this.supabaseManager = new SupabaseManager();
            const success = await this.supabaseManager.initialize(supabaseUrl, supabaseKey);
            
            if (success) {
                this.setupSupabaseEventHandlers();
                this.setupCanvasEventHandlers();
                console.log('Supabase collaboration initialized successfully');
                return true;
            }
            return false;
        } catch (error) {
            console.error('Failed to initialize Supabase collaboration:', error);
            return false;
        }
    }
    
    /**
     * Set up Supabase event handlers
     */
    setupSupabaseEventHandlers() {
        this.supabaseManager.onSessionJoined = (sessionData) => {
            console.log('Session joined successfully');
            this.isCollaborating = true;
            this.updateConnectionStatus('connected');
            this.showCollaborationPanels();
        };
        
        this.supabaseManager.onSessionLeft = () => {
            console.log('Session left');
            this.isCollaborating = false;
            this.updateConnectionStatus('offline');
            this.hideCollaborationPanels();
            this.connectedUsers.clear();
            this.userCursors.clear();
        };
        
        this.supabaseManager.onCanvasObjectChanged = (payload) => {
            this.handleCanvasObjectChange(payload);
        };
        
        this.supabaseManager.onUserJoined = (userData) => {
            this.handleUserJoined(userData);
        };
        
        this.supabaseManager.onUserLeft = (userData) => {
            this.handleUserLeft(userData);
        };
        
        this.supabaseManager.onCursorMoved = (cursorData) => {
            this.handleCursorMoved(cursorData);
        };
        
        this.supabaseManager.onChatMessage = (messageData) => {
            this.handleChatMessage(messageData);
        };
        
        this.supabaseManager.onMapChanged = (mapName) => {
            this.handleMapChanged(mapName);
        };
        
        this.supabaseManager.onError = (error) => {
            console.error('Supabase error:', error);
            this.updateConnectionStatus('error');
        };
    }
    
    /**
     * Set up canvas event handlers
     */
    setupCanvasEventHandlers() {
        // Track object additions
        this.canvas.on('object:added', (e) => {
            if (this.isCollaborating && !this.canvas.isLoadingFromJSON && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.syncCanvasObject('added', e.target);
                }, e.target.id);
            }
        });
        
        // Track object modifications
        this.canvas.on('object:modified', (e) => {
            if (this.isCollaborating && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.syncCanvasObject('modified', e.target);
                }, e.target.id);
            }
        });
        
        // Track object scaling
        this.canvas.on('object:scaling', (e) => {
            if (this.isCollaborating && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.syncCanvasObject('modified', e.target);
                }, e.target.id);
            }
        });
        
        // Track object moving
        this.canvas.on('object:moving', (e) => {
            if (this.isCollaborating && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.syncCanvasObject('modified', e.target);
                }, e.target.id);
            }
        });
        
        // Track object removal
        this.canvas.on('object:removed', (e) => {
            if (this.isCollaborating && !e.target.isFromRemote) {
                this.throttledSync(() => {
                    this.syncCanvasObject('removed', e.target);
                }, e.target.id);
            }
        });
        
        // Track path creation
        this.canvas.on('path:created', (e) => {
            if (this.isCollaborating && e.path && !e.path.isFromRemote) {
                this.throttledSync(() => {
                    this.syncCanvasObject('added', e.path);
                }, e.path.id);
            }
        });
        
        // Track mouse movements for cursor sharing
        this.canvas.on('mouse:move', (e) => {
            if (this.isCollaborating) {
                this.throttledCursorUpdate(e);
            }
        });
    }
    
    /**
     * Throttled synchronization to prevent spam
     */
    throttledSync(callback, objectId = null) {
        const now = Date.now();
        
        if (objectId) {
            const lastTime = this.pendingUpdates.get(objectId) || 0;
            if (now - lastTime > this.syncThrottle) {
                this.pendingUpdates.set(objectId, now);
                callback();
            } else {
                // Schedule a delayed update
                setTimeout(() => {
                    if (this.pendingUpdates.get(objectId) === lastTime) {
                        this.pendingUpdates.set(objectId, Date.now());
                        callback();
                    }
                }, this.syncThrottle - (now - lastTime));
            }
        } else {
            if (now - this.lastSyncTime > this.syncThrottle) {
                this.lastSyncTime = now;
                callback();
            }
        }
    }
    
    /**
     * Throttled cursor updates
     */
    throttledCursorUpdate(e) {
        if (!this.lastCursorUpdate || Date.now() - this.lastCursorUpdate > 100) {
            this.lastCursorUpdate = Date.now();
            const pointer = this.canvas.getPointer(e.e);
            this.supabaseManager.updateCursor(pointer.x, pointer.y);
        }
    }
    
    /**
     * Sync canvas object to Supabase
     */
    async syncCanvasObject(action, object) {
        try {
            // Ensure object has an ID
            if (!object.id) {
                object.id = this.generateObjectId(object.type);
            }
            
            const objectData = this.serializeObject(object);
            if (!objectData) {
                console.error('Failed to serialize object:', object);
                return;
            }
            
            if (action === 'removed') {
                await this.supabaseManager.removeCanvasObject(object.id);
            } else {
                await this.supabaseManager.addCanvasObject(
                    object.id,
                    object.type,
                    objectData
                );
            }
            
            console.log(`Canvas object ${action}:`, object.id);
        } catch (error) {
            console.error(`Failed to sync canvas object (${action}):`, error);
        }
    }
    
    /**
     * Generate unique object ID
     */
    generateObjectId(type) {
        const prefix = type === 'path' ? 'path' : 'obj';
        return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Serialize Fabric.js object for storage
     */
    serializeObject(object) {
        try {
            const serialized = object.toObject(['id', 'isAgent', 'isDrawingPath', 'name', 'isCustomImage']);
            
            // For images, include source URL and transformation properties
            if (object.type === 'image' && object._element) {
                serialized.src = object._element.src || object.getSrc();
                serialized.scaleX = object.scaleX;
                serialized.scaleY = object.scaleY;
                serialized.left = object.left;
                serialized.top = object.top;
                serialized.angle = object.angle;
                serialized.flipX = object.flipX;
                serialized.flipY = object.flipY;
                serialized.originX = object.originX;
                serialized.originY = object.originY;
                serialized.width = object.width;
                serialized.height = object.height;
            }
            
            return serialized;
        } catch (error) {
            console.error('Error serializing object:', error);
            return null;
        }
    }
    
    /**
     * Deserialize object data back to Fabric.js object
     */
    async deserializeObject(objectData) {
        try {
            let object;
            
            switch (objectData.type) {
                case 'image':
                    if (objectData.src) {
                        return new Promise((resolve) => {
                            fabric.Image.fromURL(objectData.src, (img) => {
                                if (img) {
                                    img.set(objectData);
                                    img.isFromRemote = true;
                                    resolve(img);
                                } else {
                                    console.error('Failed to load image from URL:', objectData.src);
                                    resolve(null);
                                }
                            }, { crossOrigin: 'Anonymous' });
                        });
                    }
                    return null;
                    
                case 'path':
                    object = new fabric.Path(objectData.path, objectData);
                    break;
                    
                case 'line':
                    object = new fabric.Line([objectData.x1, objectData.y1, objectData.x2, objectData.y2], objectData);
                    break;
                    
                case 'rect':
                    object = new fabric.Rect(objectData);
                    break;
                    
                case 'circle':
                    object = new fabric.Circle(objectData);
                    break;
                    
                case 'triangle':
                    object = new fabric.Triangle(objectData);
                    break;
                    
                case 'group':
                    const objects = await Promise.all(
                        objectData.objects.map(obj => this.deserializeObject(obj))
                    );
                    object = new fabric.Group(objects.filter(Boolean), objectData);
                    break;
                    
                default:
                    console.warn('Unknown object type:', objectData.type);
                    return null;
            }
            
            if (object) {
                object.isFromRemote = true;
            }
            
            return object;
        } catch (error) {
            console.error('Error deserializing object:', error);
            return null;
        }
    }

    /**
     * Handle canvas object changes from Supabase
     */
    async handleCanvasObjectChange(payload) {
        try {
            console.log('Canvas object change received:', payload);

            const wasCollaborating = this.isCollaborating;
            this.isCollaborating = false; // Temporarily disable to prevent echo

            if (payload.eventType === 'DELETE' || payload.new?.is_deleted) {
                // Handle object removal
                const objectId = payload.old?.object_id || payload.new?.object_id;
                const existingObject = this.findObjectById(objectId);
                if (existingObject) {
                    existingObject.isFromRemote = true;
                    this.canvas.remove(existingObject);
                }
            } else if (payload.new) {
                // Handle object addition/modification
                const objectData = payload.new.object_data;
                const objectResult = await this.deserializeObject(objectData);

                let object;
                if (objectResult instanceof Promise) {
                    object = await objectResult;
                } else {
                    object = objectResult;
                }

                if (object) {
                    object.isFromRemote = true;

                    if (payload.eventType === 'INSERT') {
                        this.canvas.add(object);
                    } else if (payload.eventType === 'UPDATE') {
                        const existingObject = this.findObjectById(object.id);
                        if (existingObject) {
                            existingObject.isFromRemote = true;
                            existingObject.set(object);
                            existingObject.setCoords();
                            if (existingObject.isAgent) {
                                this.canvas.bringToFront(existingObject);
                            }
                        } else {
                            this.canvas.add(object);
                        }
                    }
                }
            }

            this.canvas.renderAll();

            // Re-enable collaboration after processing
            setTimeout(() => {
                this.isCollaborating = wasCollaborating;
            }, 50);

        } catch (error) {
            console.error('Error handling canvas object change:', error);
        }
    }

    /**
     * Handle user joined event
     */
    handleUserJoined(userData) {
        console.log('User joined:', userData.user_name);
        this.connectedUsers.set(userData.user_id, {
            userName: userData.user_name,
            isHost: userData.is_host,
            joinTime: userData.joined_at
        });
        this.updateUsersDisplay();
    }

    /**
     * Handle user left event
     */
    handleUserLeft(userData) {
        console.log('User left:', userData.user_name);
        this.connectedUsers.delete(userData.user_id);
        this.removeUserCursor(userData.user_id);
        this.updateUsersDisplay();
    }

    /**
     * Handle cursor movement
     */
    handleCursorMoved(cursorData) {
        this.updateUserCursor(cursorData.user_id, cursorData.user_name, cursorData.x, cursorData.y);
    }

    /**
     * Handle chat message
     */
    handleChatMessage(messageData) {
        this.addChatMessage(messageData, false);
    }

    /**
     * Handle map change
     */
    handleMapChanged(mapName) {
        console.log('Map changed to:', mapName);

        const mapSelect = document.getElementById('map-select');
        if (mapSelect && mapSelect.value !== mapName) {
            const originalHandler = mapSelect.onchange;
            mapSelect.onchange = null;

            mapSelect.value = mapName;
            if (window.currentMap !== undefined) {
                window.currentMap = mapName;
            }

            if (window.loadMap && typeof window.loadMap === 'function') {
                window.loadMap(mapName);
            }

            setTimeout(() => {
                mapSelect.onchange = originalHandler;
            }, 100);
        }
    }

    /**
     * Create a new session
     */
    async createSession(sessionName, userName) {
        try {
            const result = await this.supabaseManager.createSession(sessionName, userName);
            console.log('Session created:', result.sessionCode);
            return result;
        } catch (error) {
            console.error('Failed to create session:', error);
            throw error;
        }
    }

    /**
     * Join an existing session
     */
    async joinSession(sessionCode, userName) {
        try {
            const result = await this.supabaseManager.joinSession(sessionCode, userName);

            // Load existing canvas objects
            const canvasObjects = await this.supabaseManager.loadCanvasState();
            await this.loadCanvasObjects(canvasObjects);

            console.log('Session joined:', sessionCode);
            return result;
        } catch (error) {
            console.error('Failed to join session:', error);
            throw error;
        }
    }

    /**
     * Load canvas objects from database
     */
    async loadCanvasObjects(canvasObjects) {
        try {
            this.canvas.isLoadingFromJSON = true;

            for (const dbObject of canvasObjects) {
                const objectData = dbObject.object_data;
                const objectResult = await this.deserializeObject(objectData);

                let object;
                if (objectResult instanceof Promise) {
                    object = await objectResult;
                } else {
                    object = objectResult;
                }

                if (object) {
                    object.isFromRemote = true;
                    this.canvas.add(object);
                }
            }

            this.canvas.renderAll();
        } catch (error) {
            console.error('Error loading canvas objects:', error);
        } finally {
            this.canvas.isLoadingFromJSON = false;
        }
    }

    /**
     * Update map
     */
    async updateMap(mapName) {
        try {
            await this.supabaseManager.updateMap(mapName);
        } catch (error) {
            console.error('Failed to update map:', error);
        }
    }

    /**
     * Send chat message
     */
    async sendChatMessage(message) {
        try {
            await this.supabaseManager.sendChatMessage(message);

            // Add to local chat immediately
            const currentUser = this.supabaseManager.getCurrentUser();
            this.addChatMessage({
                user_name: currentUser.name,
                message: message,
                created_at: new Date().toISOString()
            }, true);
        } catch (error) {
            console.error('Failed to send chat message:', error);
        }
    }

    /**
     * Disconnect from session
     */
    async disconnect() {
        try {
            await this.supabaseManager.leaveSession();
            this.isCollaborating = false;
            this.connectedUsers.clear();
            this.userCursors.clear();
            this.updateConnectionStatus('offline');
            this.hideCollaborationPanels();
        } catch (error) {
            console.error('Failed to disconnect:', error);
        }
    }

    /**
     * Utility methods (reused from original collaboration manager)
     */
    findObjectById(id) {
        return this.canvas.getObjects().find(obj => obj.id === id);
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connection-status');
        const statusText = document.querySelector('#connection-status .status-text');

        if (statusElement && statusText) {
            statusElement.className = `connection-status ${status}`;

            switch (status) {
                case 'connected':
                    statusText.textContent = `Connected (${this.connectedUsers.size + 1} users)`;
                    break;
                case 'connecting':
                    statusText.textContent = 'Connecting...';
                    break;
                case 'offline':
                    statusText.textContent = 'Offline';
                    break;
                case 'error':
                    statusText.textContent = 'Connection Error';
                    break;
                default:
                    statusText.textContent = 'Unknown';
            }
        }
    }

    showCollaborationPanels() {
        const usersPanel = document.getElementById('users-panel');
        const chatPanel = document.getElementById('chat-panel');

        if (usersPanel) usersPanel.style.display = 'block';
        if (chatPanel) chatPanel.style.display = 'block';

        this.updateUsersDisplay();
        this.setupChatHandlers();
    }

    hideCollaborationPanels() {
        const usersPanel = document.getElementById('users-panel');
        const chatPanel = document.getElementById('chat-panel');

        if (usersPanel) usersPanel.style.display = 'none';
        if (chatPanel) chatPanel.style.display = 'none';
    }

    updateUsersDisplay() {
        const usersContainer = document.getElementById('connected-users');
        if (!usersContainer) return;

        usersContainer.innerHTML = '';

        // Add local user
        const currentUser = this.supabaseManager?.getCurrentUser();
        if (currentUser) {
            const localUserDiv = document.createElement('div');
            localUserDiv.className = 'user-item local-user';
            localUserDiv.innerHTML = `
                <span class="user-indicator"></span>
                <span class="user-name">${currentUser.name} (You)</span>
            `;
            usersContainer.appendChild(localUserDiv);
        }

        // Add connected users
        this.connectedUsers.forEach((user, userId) => {
            const userDiv = document.createElement('div');
            userDiv.className = 'user-item';
            userDiv.innerHTML = `
                <span class="user-indicator"></span>
                <span class="user-name">${user.userName}</span>
            `;
            usersContainer.appendChild(userDiv);
        });
    }

    updateUserCursor(userId, userName, x, y) {
        // Placeholder for cursor visualization
        console.log(`User ${userName} cursor at (${x}, ${y})`);
    }

    removeUserCursor(userId) {
        this.userCursors.delete(userId);
    }

    setupChatHandlers() {
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-chat');

        if (chatInput && sendButton) {
            // Remove existing listeners to prevent duplicates
            const newChatInput = chatInput.cloneNode(true);
            const newSendButton = sendButton.cloneNode(true);
            chatInput.parentNode.replaceChild(newChatInput, chatInput);
            sendButton.parentNode.replaceChild(newSendButton, sendButton);

            // Add new listeners
            newSendButton.addEventListener('click', () => {
                this.handleChatSend(newChatInput);
            });

            newChatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleChatSend(newChatInput);
                }
            });
        }
    }

    handleChatSend(chatInput) {
        const message = chatInput.value.trim();
        if (message) {
            this.sendChatMessage(message);
            chatInput.value = '';
        }
    }

    addChatMessage(data, isOwn) {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${isOwn ? 'own' : 'other'}`;

        const timestamp = new Date(data.created_at).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="sender">${data.user_name}</div>
            <div class="content">${this.escapeHtml(data.message)}</div>
            <div class="timestamp">${timestamp}</div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Check if currently collaborating
     */
    isInSession() {
        return this.supabaseManager?.isInSession() || false;
    }

    /**
     * Get current session info
     */
    getCurrentSession() {
        return this.supabaseManager?.getCurrentSession() || null;
    }

    /**
     * Get current user info
     */
    getCurrentUser() {
        return this.supabaseManager?.getCurrentUser() || null;
    }
}

// Export for use in other files
window.SupabaseCollaborationManager = SupabaseCollaborationManager;
