{"name": "valo-strat-board-server", "version": "1.0.0", "description": "WebSocket server for Valorant Strategy Board real-time collaboration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["websocket", "collaboration", "valorant", "strategy", "real-time"], "author": "Valorant Strategy Board", "license": "MIT", "dependencies": {"ws": "^8.14.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}