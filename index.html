<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta Tags -->
    <title>Valorant Strategy Board - Real-time Team Collaboration Tool</title>
    <meta name="description" content="Professional Valorant strategy board with real-time collaboration. Plan tactics, draw strategies, and coordinate with your team on all Valorant maps including Ascent, Bind, Haven, Split, and more.">
    <meta name="keywords" content="Valorant, strategy board, tactical planning, team collaboration, esports, gaming, real-time drawing, map strategies">
    <meta name="author" content="Valorant Strategy Board">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="https://yourdomain.com/">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Valorant Strategy Board - Real-time Team Collaboration">
    <meta property="og:description" content="Plan winning strategies with your team using our interactive Valorant strategy board. Real-time collaboration on all maps.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://yourdomain.com/">
    <meta property="og:image" content="https://yourdomain.com/images/og-image.jpg">
    <meta property="og:site_name" content="Valorant Strategy Board">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Valorant Strategy Board - Real-time Team Collaboration">
    <meta name="twitter:description" content="Plan winning strategies with your team using our interactive Valorant strategy board.">
    <meta name="twitter:image" content="https://yourdomain.com/images/twitter-card.jpg">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebApplication",
        "name": "Valorant Strategy Board",
        "description": "Real-time collaborative strategy board for Valorant teams",
        "url": "https://yourdomain.com/",
        "applicationCategory": "GameApplication",
        "operatingSystem": "Web Browser",
        "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
        }
    }
    </script>

    <link rel="stylesheet" href="css/styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.1/fabric.min.js"></script>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        #debug-console {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: #00ff00;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-width: 400px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 9999;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Mobile Warning -->
    <div id="mobile-warning" class="mobile-warning">
        <div class="mobile-warning-content">
            <h2>🖥️ Desktop Required</h2>
            <p>This Valorant Strategy Board is optimized for desktop and tablet devices with larger screens.</p>
            <p>For the best experience with drawing tools and collaboration features, please use:</p>
            <ul>
                <li>Desktop computer</li>
                <li>Laptop</li>
                <li>Tablet in landscape mode</li>
            </ul>
            <button id="continue-mobile" class="continue-btn">Continue Anyway</button>
        </div>
    </div>

    <div class="container">
        <header>
            <h1>Valorant Strategy Board</h1>
            <div class="header-controls">
                <div class="map-selector">
                    <label for="map-select">Select Map:</label>
                    <select id="map-select">
                        <option value="Ascent">Ascent</option>
                        <option value="Bind">Bind</option>
                        <option value="Haven">Haven</option>
                        <option value="Split">Split</option>
                        <option value="Icebox">Icebox</option>
                        <option value="Pearl">Pearl</option>
                        <option value="Fracture">Fracture</option>
                        <option value="Lotus">Lotus</option>
                        <option value="Sunset">Sunset</option>
                        <option value="Abyss">Abyss</option>
                    </select>
                </div>

                <!-- Collaboration Controls -->
                <div class="collaboration-controls">
                    <button id="collaborate-btn" class="collab-btn primary">
                        <span class="tooltip">Start Collaboration</span>
                        👥 Collaborate
                    </button>

                    <!-- Session Info Display -->
                    <div id="session-info" class="session-info-display" style="display: none;">
                        <div class="session-code-display-header">
                            <span class="session-label">Session:</span>
                            <span id="current-session-code" class="session-code-text">ABC123</span>
                            <button id="copy-current-code" class="copy-code-btn" title="Copy session code">📋</button>
                        </div>
                    </div>

                    <div id="connection-status" class="connection-status offline">
                        <span class="status-indicator"></span>
                        <span class="status-text">Offline</span>
                    </div>
                </div>
            </div>

            <!-- Help Button -->
            <button id="help-btn" class="help-btn" title="Show Controls & Shortcuts">❓</button>
        </header>

        <main>
            <div class="canvas-container">
                <div class="canvas-wrapper">
                    <canvas id="strategy-canvas"></canvas>
                </div>
            </div>

            <div class="sidebar">
                <div class="agents-panel">
                    <h3>Agents</h3>
                    <div class="agent-icons" id="agent-icons">
                        <!-- Agent icons will be loaded here dynamically -->
                    </div>
                </div>

                <!-- Custom Images Panel -->
                <div class="images-panel">
                    <h3>Custom Images</h3>
                    <div class="image-upload-section">
                        <input type="file" id="image-upload" accept="image/*" style="display: none;">
                        <button id="upload-image-btn" class="upload-btn">📷 Upload Image</button>
                        <div class="upload-info">
                            <small>Supports: JPG, PNG, GIF, WebP</small>
                        </div>
                    </div>
                    <div class="custom-images" id="custom-images">
                        <!-- Custom uploaded images will appear here -->
                    </div>
                </div>

                <!-- Connected Users Panel -->
                <div class="users-panel" id="users-panel" style="display: none;">
                    <h3>Connected Users</h3>
                    <div class="connected-users" id="connected-users">
                        <!-- Connected users will be shown here -->
                    </div>
                </div>

                <!-- Chat Panel -->
                <div class="chat-panel" id="chat-panel" style="display: none;">
                    <h3>Team Chat</h3>
                    <div class="chat-messages" id="chat-messages">
                        <!-- Chat messages will appear here -->
                    </div>
                    <div class="chat-input-container">
                        <input type="text" id="chat-input" placeholder="Type a message..." maxlength="200">
                        <button id="send-chat" title="Send message">📤</button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Help Modal -->
        <div id="help-modal" class="modal" style="display: none;">
            <div class="modal-content help-modal-content">
                <div class="modal-header">
                    <h2>🎮 Controls & Shortcuts</h2>
                    <span class="close" id="close-help">&times;</span>
                </div>
                <div class="modal-body help-modal-body">
                    <div class="help-section">
                        <h3>🖱️ Drawing Tools</h3>
                        <div class="help-grid">
                            <div class="help-item">
                                <kbd>P</kbd>
                                <span>Pencil Tool - Free drawing</span>
                            </div>
                            <div class="help-item">
                                <kbd>L</kbd>
                                <span>Line Tool - Draw straight lines</span>
                            </div>
                            <div class="help-item">
                                <kbd>R</kbd>
                                <span>Rectangle Tool - Draw rectangles</span>
                            </div>
                            <div class="help-item">
                                <kbd>C</kbd>
                                <span>Circle Tool - Draw circles</span>
                            </div>
                            <div class="help-item">
                                <kbd>A</kbd>
                                <span>Arrow Tool - Draw arrows</span>
                            </div>
                            <div class="help-item">
                                <kbd>T</kbd>
                                <span>Text Tool - Add text labels</span>
                            </div>
                            <div class="help-item">
                                <kbd>E</kbd>
                                <span>Eraser Tool - Remove objects</span>
                            </div>
                        </div>
                    </div>

                    <div class="help-section">
                        <h3>⌨️ Canvas Controls</h3>
                        <div class="help-grid">
                            <div class="help-item">
                                <kbd>Ctrl</kbd> + <kbd>Z</kbd>
                                <span>Undo last action</span>
                            </div>
                            <div class="help-item">
                                <kbd>Ctrl</kbd> + <kbd>Y</kbd>
                                <span>Redo last action</span>
                            </div>
                            <div class="help-item">
                                <kbd>Ctrl</kbd> + <kbd>+</kbd>
                                <span>Zoom in</span>
                            </div>
                            <div class="help-item">
                                <kbd>Ctrl</kbd> + <kbd>-</kbd>
                                <span>Zoom out</span>
                            </div>
                            <div class="help-item">
                                <kbd>Ctrl</kbd> + <kbd>0</kbd>
                                <span>Reset zoom</span>
                            </div>
                            <div class="help-item">
                                <kbd>B</kbd>
                                <span>Bring agents to front</span>
                            </div>
                            <div class="help-item">
                                <kbd>Delete</kbd>
                                <span>Delete selected object</span>
                            </div>
                        </div>
                    </div>

                    <div class="help-section">
                        <h3>👥 Collaboration</h3>
                        <div class="help-grid">
                            <div class="help-item">
                                <span class="help-icon">🏠</span>
                                <span>Host Session - Create a new collaboration session</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🚪</span>
                                <span>Join Session - Join existing session with 6-character code</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">💬</span>
                                <span>Team Chat - Communicate with your team in real-time</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">👁️</span>
                                <span>Live Sync - See changes from teammates instantly</span>
                            </div>
                        </div>
                    </div>

                    <div class="help-section">
                        <h3>🎯 Agent Management</h3>
                        <div class="help-grid">
                            <div class="help-item">
                                <span class="help-icon">🖱️</span>
                                <span>Drag & Drop - Add agents to the map</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🔄</span>
                                <span>Resize - Drag corners to resize agents</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">📍</span>
                                <span>Move - Click and drag to reposition</span>
                            </div>
                            <div class="help-item">
                                <kbd>B</kbd>
                                <span>If agents get covered by drawings, press B to bring them to front</span>
                            </div>
                        </div>
                    </div>

                    <div class="help-section">
                        <h3>🖼️ Custom Images</h3>
                        <div class="help-grid">
                            <div class="help-item">
                                <span class="help-icon">📷</span>
                                <span>Upload Button - Add custom images to your strategy</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🖱️</span>
                                <span>Drag & Drop - Drop image files directly onto the canvas</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🔄</span>
                                <span>Resize & Move - Just like agents, custom images can be resized and repositioned</span>
                            </div>
                            <div class="help-item">
                                <span class="help-icon">🌐</span>
                                <span>Real-time Sync - Custom images are shared with all team members instantly</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer>
            <div class="drawing-tools">
                <button id="select-tool" class="tool-btn active" data-tool="select"><span class="tooltip">Select</span>✋</button>
                <button id="pencil-tool" class="tool-btn" data-tool="pencil"><span class="tooltip">Pencil</span>✏️</button>
                <button id="line-tool" class="tool-btn" data-tool="line"><span class="tooltip">Line</span>⟋</button>
                <button id="arrow-tool" class="tool-btn" data-tool="arrow"><span class="tooltip">Arrow</span>→</button>
                <button id="rect-tool" class="tool-btn" data-tool="rect"><span class="tooltip">Rectangle</span>□</button>
                <button id="circle-tool" class="tool-btn" data-tool="circle"><span class="tooltip">Circle</span>○</button>
                <button id="eraser-tool" class="tool-btn" data-tool="eraser"><span class="tooltip">Eraser</span>🧽</button>

                <div class="color-picker">
                    <label for="color-select">Color:</label>
                    <input type="color" id="color-select" value="#FF4655">
                </div>

                <div class="brush-size">
                    <label for="brush-size">Size:</label>
                    <input type="range" id="brush-size" min="1" max="20" value="3">
                </div>
            </div>

            <div class="strategy-controls">
                <button id="clear-btn">Clear</button>
                <button id="undo-btn">Undo</button>
                <button id="redo-btn">Redo</button>
                <input type="text" id="strategy-name" placeholder="Strategy Name">
                <button id="save-btn">Save</button>
                <button id="load-btn">Load</button>
                <button id="debug-btn" onclick="toggleDebug()">Debug</button>
            </div>
        </footer>
    </div>

    <div id="load-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Load Strategy</h2>
            <div id="saved-strategies-list"></div>
        </div>
    </div>

    <!-- Collaboration Modal -->
    <div id="collaboration-modal" class="modal">
        <div class="modal-content">
            <span class="close" id="collab-modal-close">&times;</span>
            <h2>Real-time Collaboration</h2>

            <div id="collab-mode-selector" class="collab-mode-selector">
                <p>Choose how you want to collaborate with your team:</p>
                <div class="collab-mode-buttons">
                    <button id="host-session-btn" class="collab-mode-btn primary">
                        <i class="icon">🏠</i>
                        <span class="btn-title">Host Session</span>
                        <span class="btn-desc">Create a new collaboration session</span>
                    </button>
                    <button id="join-session-btn" class="collab-mode-btn secondary">
                        <i class="icon">🔗</i>
                        <span class="btn-title">Join Session</span>
                        <span class="btn-desc">Join an existing session</span>
                    </button>
                </div>
            </div>

            <!-- Host Session Panel -->
            <div id="host-panel" class="collab-panel" style="display: none;">
                <h3>Host a Collaboration Session</h3>
                <div class="session-info">
                    <label for="session-name">Session Name:</label>
                    <input type="text" id="session-name" placeholder="Enter session name" maxlength="30">
                </div>
                <div class="session-code-display">
                    <label>Share this code with your team:</label>
                    <div class="code-container">
                        <input type="text" id="host-session-code" readonly>
                        <button id="copy-session-code" class="copy-btn">📋 Copy</button>
                    </div>
                </div>
                <div id="host-status" class="status-message">Setting up session...</div>
                <div class="panel-actions">
                    <button id="start-hosting" class="action-btn primary">Start Session</button>
                    <button id="cancel-host" class="action-btn secondary">Cancel</button>
                </div>
            </div>

            <!-- Join Session Panel -->
            <div id="join-panel" class="collab-panel" style="display: none;">
                <h3>Join a Collaboration Session</h3>
                <div class="session-info">
                    <label for="join-session-code">Session Code:</label>
                    <input type="text" id="join-session-code" placeholder="Enter 6-character code" maxlength="6">
                </div>
                <div id="join-status" class="status-message">Enter the session code to connect</div>
                <div class="panel-actions">
                    <button id="connect-session" class="action-btn primary">Connect</button>
                    <button id="cancel-join" class="action-btn secondary">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <div id="debug-console"></div>

    <script>
        // Debug console
        function toggleDebug() {
            const debugConsole = document.getElementById('debug-console');
            debugConsole.style.display = debugConsole.style.display === 'none' ? 'block' : 'none';
        }

        // Override console.log and console.error
        const originalLog = console.log;
        const originalError = console.error;
        const debugConsole = document.getElementById('debug-console');

        console.log = function() {
            // Call original console.log
            originalLog.apply(console, arguments);

            // Add to debug console
            const msg = Array.from(arguments).join(' ');
            const logElement = document.createElement('div');
            logElement.textContent = '> ' + msg;
            debugConsole.appendChild(logElement);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        };

        console.error = function() {
            // Call original console.error
            originalError.apply(console, arguments);

            // Add to debug console with error styling
            const msg = Array.from(arguments).join(' ');
            const logElement = document.createElement('div');
            logElement.textContent = '! ' + msg;
            logElement.style.color = '#ff5555';
            debugConsole.appendChild(logElement);
            debugConsole.scrollTop = debugConsole.scrollHeight;
        };
    </script>

    <!-- Local development configuration (only for development) -->
    <script src="js/local-config.js"></script>
    <script src="js/config.js"></script>
    <script src="js/supabase-client.js"></script>
    <script src="js/supabase-collaboration.js"></script>
    <script src="js/websocket-manager.js"></script>
    <script src="js/collaboration.js"></script>
    <script src="js/app.js"></script>
</body>
</html>